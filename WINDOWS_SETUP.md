# Windows虚拟机Qt Creator配置指南

## 问题说明

在Windows虚拟机中使用Qt Creator访问macOS主机文件会导致"Persisting CMake state"问题，这是由于：

1. **跨平台路径映射问题**
2. **文件系统权限冲突**
3. **CMake工具链不匹配**
4. **虚拟机文件共享延迟**

## 解决方案

### 方案1: 使用Windows批处理脚本 (推荐)

```batch
# 构建项目
build_windows.bat

# 运行项目
run_windows.bat
```

### 方案2: Qt Creator正确配置步骤

1. **关闭Qt Creator**

2. **删除所有缓存文件**：
   - 删除 `CMakeLists.txt.user`
   - 删除 `build` 目录

3. **重新打开Qt Creator**：
   - File -> Open File or Project
   - 选择 `CMakeLists.txt`

4. **配置项目**：
   - Kit: 选择Windows MinGW或MSVC
   - Build Directory: 设置为项目内的 `build` 目录
   - CMake参数: `-DUSE_UI_FILES=ON -DCMAKE_BUILD_TYPE=Debug`

5. **重要设置**：
   - 在Projects -> Build Settings中
   - 确保"Build directory"指向正确路径
   - 添加CMake参数：`-G "MinGW Makefiles"`

### 方案3: 命令行工作流程

```batch
# 1. 清理环境
rmdir /s /q build
del CMakeLists.txt.user

# 2. 创建构建目录
mkdir build
cd build

# 3. 配置CMake
cmake -G "MinGW Makefiles" -DUSE_UI_FILES=ON ..

# 4. 构建
cmake --build . --parallel 4

# 5. 运行
aiwuu-classroom.exe
```

## 虚拟机特定注意事项

### 1. 文件共享设置
- 确保虚拟机文件共享已正确配置
- 建议使用"完全访问"权限
- 避免使用符号链接

### 2. 路径问题
- 使用相对路径而非绝对路径
- 避免包含空格的路径
- 确保路径分隔符正确

### 3. 性能优化
- 关闭实时病毒扫描对项目目录的监控
- 增加虚拟机内存分配
- 使用SSD存储

### 4. Qt Creator设置
- 禁用自动CMake重新配置
- 关闭文件监控功能
- 使用手动构建模式

## 故障排除

### 问题1: "Persisting CMake state"卡住
**解决方案**：
1. 强制关闭Qt Creator
2. 删除build目录和.user文件
3. 使用批处理脚本重新构建

### 问题2: 路径找不到
**解决方案**：
1. 检查虚拟机文件共享设置
2. 确保项目路径不包含特殊字符
3. 使用Windows风格的路径分隔符

### 问题3: 编译错误
**解决方案**：
1. 确保Qt5已正确安装
2. 检查MinGW或MSVC工具链
3. 验证CMake版本兼容性

### 问题4: 运行时错误
**解决方案**：
1. 确保Qt5 DLL在PATH中
2. 检查依赖库是否完整
3. 使用windeployqt部署工具

## 推荐工作流程

1. **开发阶段**：
   - 在Qt Creator中编辑代码
   - 使用批处理脚本构建
   - 命令行运行测试

2. **调试阶段**：
   - 使用Qt Creator的调试器
   - 确保符号文件正确生成
   - 设置断点和监视

3. **发布阶段**：
   - 使用Release配置构建
   - 运行windeployqt打包
   - 测试独立运行

## 最佳实践

1. **定期清理**：定期删除build目录重新构建
2. **版本控制**：不要提交.user文件和build目录
3. **备份配置**：保存有效的Qt Creator配置
4. **文档记录**：记录有效的配置参数
