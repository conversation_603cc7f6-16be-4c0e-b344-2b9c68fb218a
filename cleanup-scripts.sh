#!/bin/bash

# 清理多余的脚本和配置文件
# 删除过时的Qt Creator配置脚本和开发环境脚本

echo "开始清理多余的脚本文件..."

# 要删除的文件列表
FILES_TO_DELETE=(
    "setup-qtcreator.sh"
    "setup-qtcreator-4.5.sh" 
    "setup-qtcreator-simple.sh"
    "qt-creator-fix.sh"
    "setup-dev-env.sh"
    "CMakeLists.txt.backup"
    "CMakeLists.txt.simple"
    ".qtcreator"
)

# 删除文件
for file in "${FILES_TO_DELETE[@]}"; do
    if [ -e "$file" ]; then
        echo "删除: $file"
        rm -rf "$file"
    else
        echo "跳过: $file (不存在)"
    fi
done

echo ""
echo "清理完成！保留的核心脚本："
echo "✅ dev.sh - 开发构建脚本"
echo "✅ build.sh - 生产打包脚本" 
echo "✅ switch_ui_mode.sh - UI模式切换脚本"

echo ""
echo "已删除的多余文件："
echo "❌ setup-qtcreator*.sh - Qt Creator配置脚本（功能已过时）"
echo "❌ qt-creator-fix.sh - 一次性问题修复脚本"
echo "❌ setup-dev-env.sh - 开发环境脚本（功能已集成到CMakeLists.txt）"
echo "❌ CMakeLists.txt.backup/.simple - 备份文件"
echo "❌ .qtcreator/ - Qt Creator配置目录"

echo ""
echo "建议：如果您不再使用Qt Creator，也可以删除 CMakeLists.txt.user 文件" 