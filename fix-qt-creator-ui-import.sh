#!/bin/bash

# Qt Creator UI目录导入修复脚本
# 解决Qt Creator不显示ui目录的问题

echo "🔧 修复Qt Creator UI目录导入问题..."
echo "================================"
echo ""

# 1. 检查当前状态
echo "📋 检查当前项目状态："
echo ""

# 检查ui目录是否存在
if [ -d "ui" ]; then
    echo "✅ ui目录存在"
    echo "   包含文件："
    ls -la ui/
    echo ""
else
    echo "❌ ui目录不存在"
    echo "请先创建ui目录和.ui文件"
    exit 1
fi

# 检查当前模式
echo "📊 当前项目模式："
./switch_ui_mode.sh status
echo ""

# 2. 检查CMakeLists.txt配置
echo "🔍 检查CMakeLists.txt配置："
if grep -q "file(GLOB UI_FILES" CMakeLists.txt; then
    echo "✅ CMakeLists.txt已配置UI文件收集"
else
    echo "❌ CMakeLists.txt缺少UI文件配置"
fi

if grep -q "CMAKE_AUTOUIC" CMakeLists.txt; then
    echo "✅ CMakeLists.txt已启用AUTOUIC"
else
    echo "❌ CMakeLists.txt缺少AUTOUIC配置"
fi
echo ""

# 3. 强制切换到UI文件模式
echo "🔄 确保项目处于UI文件模式："
./switch_ui_mode.sh ui
echo ""

# 4. 清理并重新配置
echo "🧹 清理构建缓存："
if [ -d "build" ]; then
    rm -rf build/CMakeCache.txt
    rm -rf build/CMakeFiles
    rm -rf build/*.cmake
    echo "✅ 已清理CMake缓存文件"
else
    echo "ℹ️ build目录不存在，跳过清理"
fi
echo ""

# 5. 删除Qt Creator用户配置文件
echo "🗑️ 清理Qt Creator配置："
if [ -f "CMakeLists.txt.user" ]; then
    rm CMakeLists.txt.user
    echo "✅ 已删除CMakeLists.txt.user"
else
    echo "ℹ️ CMakeLists.txt.user不存在"
fi
echo ""

# 6. 创建临时的CMakeLists.txt来强制显示UI文件
echo "📝 创建Qt Creator友好的CMakeLists.txt配置："

# 备份原文件
cp CMakeLists.txt CMakeLists.txt.backup

# 在CMakeLists.txt中添加显式的UI文件列表
cat >> CMakeLists.txt << 'EOF'

# Qt Creator UI文件显示配置
# 显式列出所有UI文件以确保Qt Creator能够识别
set(UI_FILES_EXPLICIT
    ui/loginwindow.ui
    ui/mainwindow.ui
    ui/capture.ui
)

# 将UI文件添加到项目中（即使在纯代码模式下也显示）
if(NOT USE_UI_FILES)
    # 在纯代码模式下也将UI文件添加到项目中，仅用于Qt Creator显示
    list(APPEND SOURCES ${UI_FILES_EXPLICIT})
endif()

# 设置UI文件属性，确保Qt Creator识别
set_source_files_properties(${UI_FILES_EXPLICIT} PROPERTIES
    HEADER_FILE_ONLY TRUE
)
EOF

echo "✅ 已添加UI文件显示配置"
echo ""

# 7. 重新配置CMake
echo "⚙️ 重新配置CMake："
cd build 2>/dev/null || mkdir build && cd build
cmake .. -DUSE_UI_FILES=ON
echo ""

# 8. 提供Qt Creator操作指南
echo "📖 Qt Creator操作指南："
echo ""
echo "步骤1: 关闭Qt Creator中的当前项目"
echo "1. 在Qt Creator中关闭项目 (File -> Close Project)"
echo ""

echo "步骤2: 重新打开项目"
echo "1. File -> Open File or Project"
echo "2. 选择项目根目录的 CMakeLists.txt"
echo "3. 在Configure Project页面选择合适的Kit"
echo "4. 点击 Configure Project"
echo ""

echo "步骤3: 检查项目树"
echo "1. 项目配置完成后，在左侧项目树中应该能看到："
echo "   📁 aiwuu-classroom"
echo "   ├── 📁 Sources"
echo "   ├── 📁 Headers"
echo "   ├── 📁 UI Files (或 Forms)"
echo "   │   ├── 📄 loginwindow.ui"
echo "   │   ├── 📄 mainwindow.ui"
echo "   │   └── 📄 capture.ui"
echo "   └── 📁 Resources"
echo ""

echo "步骤4: 测试UI文件"
echo "1. 双击任意 .ui 文件"
echo "2. 应该在Qt Designer中打开"
echo "3. 可以进行可视化编辑"
echo ""

echo "🔍 故障排除："
echo ""
echo "问题1: 仍然看不到UI Files文件夹"
echo "解决方案:"
echo "- 确保项目处于UI文件模式 (USE_UI_FILES=ON)"
echo "- 检查Qt Creator版本是否支持.ui文件"
echo "- 尝试手动添加UI文件到项目"
echo ""

echo "问题2: UI文件显示但无法打开"
echo "解决方案:"
echo "- 检查Qt Designer插件是否启用"
echo "- 运行 ./check-qt-designer.sh 检查插件状态"
echo "- 尝试右键 -> Open With -> Qt Designer"
echo ""

echo "问题3: 项目树结构异常"
echo "解决方案:"
echo "- 删除 CMakeLists.txt.user 重新配置"
echo "- 清理build目录重新构建"
echo "- 检查CMakeLists.txt语法是否正确"
echo ""

echo "💡 手动添加UI文件的方法："
echo "如果自动导入失败，可以手动添加："
echo "1. 在Qt Creator中右键项目根目录"
echo "2. 选择 'Add Existing Files...'"
echo "3. 选择 ui/ 目录下的所有 .ui 文件"
echo "4. 点击 'Add' 添加到项目"
echo ""

echo "🔄 恢复原始配置："
echo "如果需要恢复原始的CMakeLists.txt："
echo "mv CMakeLists.txt.backup CMakeLists.txt"
echo ""

echo "✅ 修复完成！"
echo "现在请按照上述步骤重新打开Qt Creator项目。" 