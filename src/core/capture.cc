#include <algorithm>
#include <functional>
#include <future>
#include <fxmy.h>
#include <iostream>
#include <mutex>
#include <utility>
#include <vector>

#include <windows.h>

#if USE_QT

#include <QApplication>
#include <QFrame>
#include <QMainWindow>
#include <QMessageBox>
#include <QPainter>
#include <QPushButton>
#include <QUuid>
#include <QVBoxLayout>
#endif

struct ViewData {
  COLORREF text_color;
};

#define ID_BTN_CAPTURE_ALL 100
#define ID_BTN_CAPTURE_CANCEL 101

HWND g_main_window = nullptr;
HWND g_status_window = nullptr;
HWND g_static_window = nullptr;
LRESULT WINAPI WindowProc(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam);
WNDPROC g_old_static_wnd_proc = nullptr;
WNDPROC g_old_status_wnd_proc = nullptr;
HFONT g_status_font = nullptr;

void SetEnable(HWND hwnd, bool enable) { EnableWindow(hwnd, enable); }

void SetEnable(int id, HWND parent, bool enable) {
  HWND hwnd = GetDlgItem(parent, id);
  SetEnable(hwnd, enable);
}

std::vector<HWND> GetChildWindows(HWND parent) {
  std::vector<HWND> child_windows;
  HWND child = GetWindow(parent, GW_CHILD);
  while (child) {
    child_windows.push_back(child);
    child = GetWindow(child, GW_HWNDNEXT);
  }

  // sort by ID
  std::sort(child_windows.begin(), child_windows.end(), [](HWND a, HWND b) {
    auto a_id = GetDlgCtrlID(a);
    auto b_id = GetDlgCtrlID(b);
    return a_id < b_id;
  });

  return child_windows;
}

void SetStatusText(const std::wstring &text, bool error = false);

const int kFxmyMainWindowMargin = 5;

static std::once_flag g_com_init_flag;

void ComInit() {
  std::call_once(g_com_init_flag, []() { CoInitialize(NULL); });
}

// make rect offset start from (0, 0)
void NormalizeRect(RECT &rect) {
  rect.right -= rect.left;
  rect.bottom -= rect.top;
  rect.left = 0;
  rect.top = 0;
}

std::pair<int, int> GetRectSize(const RECT &rect) {
  if (rect.right < rect.left || rect.bottom < rect.top) {
    return {0, 0};
  }

  return {rect.right - rect.left, rect.bottom - rect.top};
}

RECT CalcFxmyMainWindowRect(HWND main_hwnd, HWND static_window) {
  RECT rect;
  memset(&rect, 0, sizeof(rect));
  GetClientRect(main_hwnd, &rect);
  auto rect_size = GetRectSize(rect);
  // minus the static window size
  RECT static_window_rect;
  memset(&static_window_rect, 0, sizeof(static_window_rect));
  if (static_window != NULL) {
    GetClientRect(static_window, &static_window_rect);
  }

  auto static_window_size = GetRectSize(static_window_rect);

  int view_width = rect_size.first;
  int view_height = rect_size.second - static_window_size.second;

  int target_fxmy_main_window_width = view_width - kFxmyMainWindowMargin * 2;
  int target_fxmy_main_window_height = view_height - kFxmyMainWindowMargin * 2;
  int x = rect.left + (view_width - target_fxmy_main_window_width) / 2;
  int y = rect.top + (view_height - target_fxmy_main_window_height) / 2;

  RECT fxmy_main_window_rect = {x, y, x + target_fxmy_main_window_width,
                                y + target_fxmy_main_window_height};
  return fxmy_main_window_rect;
}

void LayoutFxmyMainWindow(HWND main_hwnd, HWND fxmy_main_window) {
  RECT rect = CalcFxmyMainWindowRect(main_hwnd, g_static_window);
  auto rect_size = GetRectSize(rect);
  SetWindowPos(fxmy_main_window, HWND_TOP, rect.left, rect.top, rect_size.first,
               rect_size.second, SWP_NOZORDER);
}

void LayoutHeaderView(HWND header_view_window, HWND parent) {
  // make header view on the bottom of the parent window
  RECT rect;
  memset(&rect, 0, sizeof(rect));
  GetClientRect(parent, &rect);
  auto rect_size = GetRectSize(rect);
  int header_view_height = 50;
  SetWindowPos(header_view_window, 0, 0, rect_size.second - header_view_height,
               rect_size.first, header_view_height, SWP_NOZORDER);

  // layout child controls
  RECT header_view_rect;
  memset(&header_view_rect, 0, sizeof(header_view_rect));
  GetClientRect(header_view_window, &header_view_rect);
  auto header_view_size = GetRectSize(header_view_rect);
  int button_width = 100;
  int button_height = 30;

  // get child controls
  // layout the controls in same row and same space between them
  auto child_windows = GetChildWindows(header_view_window);
  int space = (header_view_size.first - child_windows.size() * button_width) /
              (child_windows.size() + 1);
  int x = space;
  for (auto child : child_windows) {
    // get the size of the child window
    RECT child_rect;
    memset(&child_rect, 0, sizeof(child_rect));
    GetClientRect(child, &child_rect);
    auto child_rect_size = GetRectSize(child_rect);
    int y = (header_view_size.second - child_rect_size.second) / 2;
    SetWindowPos(child, 0, x, y, button_width, button_height, SWP_NOZORDER);
    x += button_width + space;
  }
}

void LayoutStatusView(HWND status_view_window, HWND parent) {
  if (fxmy_get_state() == STATE_RUNNING) {
    return;
  }

  // make status view on the center of the parent window
  RECT rect;
  memset(&rect, 0, sizeof(rect));
  GetClientRect(parent, &rect);
  auto rect_size = GetRectSize(rect);
  int status_view_height = 80;
  double status_view_width_ratio = 0.5;

  RECT static_rect;
  memset(&static_rect, 0, sizeof(static_rect));
  GetWindowRect(g_static_window, &static_rect);
  ScreenToClient(parent, reinterpret_cast<LPPOINT>(&static_rect));

  RECT status_view_rect;
  memset(&status_view_rect, 0, sizeof(status_view_rect));
  GetClientRect(status_view_window, &status_view_rect);

  int width = rect_size.first * status_view_width_ratio;
  int height = status_view_height;
  int x = (rect_size.first - width) / 2;
  int y = (rect_size.second - height) / 2;

  HWND top = GetTopWindow(parent);
  TCHAR class_name[256] = {0};
  GetWindowText(top, class_name, 256);
  RECT top_rect;
  GetWindowRect(top, &top_rect);

  // set topmost
  SetWindowPos(status_view_window, HWND_TOP, x, y, width, height,
               SWP_SHOWWINDOW);
}

#define WM_USER_RUN_ON_UI_THREAD (WM_USER + 1)

void RunOnUIThread(const std::function<void()> &func, bool wait = true) {
#if USE_QT
#else
  auto func_ptr = new std::function<void()>(func);
  // run on the main thread
  // SendMessage to the main window to run the function
  if (wait) {
    std::promise<void> promise;
    auto future = promise.get_future();
    PostMessage(g_main_window, WM_USER_RUN_ON_UI_THREAD,
                reinterpret_cast<WPARAM>(func_ptr),
                reinterpret_cast<LPARAM>(&promise));
    future.wait();
  } else {
    PostMessage(g_main_window, WM_USER_RUN_ON_UI_THREAD,
                reinterpret_cast<WPARAM>(func_ptr), 0);
  }
#endif
}

void OnFxmyStateChanged(State state, void *user_data) {
  switch (state) {
  case STATE_INITIALIZING: {
    RunOnUIThread([] { SetStatusText(L"正在初始化..."); });
    break;
  }
  case STATE_INITIALIZED_FAILED: {
    RunOnUIThread([] { SetStatusText(L"初始化失败", true); });
    break;
  }
  case STATE_INITIALIZED: {
    RunOnUIThread([] { SetStatusText(L"初始化成功"); });
    break;
  }
  case STATE_RUNNING: {
    RunOnUIThread([] {
      SetStatusText(L"正在运行中...");
      // HIDE the status window
      ShowWindow(g_status_window, SW_HIDE);
    });
    break;
  }
  }
}

void OnDeviceFound(Device devices[], int count, void *user_data) {
  // do something
}

void OnGenTaskID(ItemID item_id, CaptureID capture_id, TaskID *task_id,
                 void *user_data) {
#if USE_QT
  // generate the task id through the callback
  QUuid uuid = QUuid::createUuid();
  // remove "{" "}" and "-" from the uuid
  QString gened_uuid = uuid.toString().remove(QRegExp("[{}-]"));
  // to lower case
  gened_uuid = gened_uuid.toLower();
  // check the id length
  if (gened_uuid.length() > sizeof(TaskID::id)) {
    return;
  }

  auto temp = gened_uuid.toStdString();
#else
  // generate the task id
  GUID guid;
  HRESULT hr = CoCreateGuid(&guid);
  if (FAILED(hr)) {
    return;
  }

  char guidString[128] = {0};
  snprintf(guidString, sizeof(guidString), "%08lx%04x%04x%04x%012llx",
           guid.Data1, guid.Data2, guid.Data3,
           (guid.Data4[0] << 8) | guid.Data4[1],
           ((unsigned long long)guid.Data4[2] << 40) |
               ((unsigned long long)guid.Data4[3] << 32) |
               ((unsigned long long)guid.Data4[4] << 24) |
               ((unsigned long long)guid.Data4[5] << 16) |
               ((unsigned long long)guid.Data4[6] << 8) | guid.Data4[7]);
  std::string temp = guidString;
#endif
  memcpy(task_id->id, temp.c_str(), temp.size());
}

Status InitializeFxmyUI(HWND parent_window_id, const UIType &ui_type, int argc,
                        char *argv[], const Rect &fxmy_main_window_rect) {
  Config config;
  memset(&config, 0, sizeof(config));
  config.ui_type = ui_type;
  config.argc = argc;
  config.argv = argv;
  config.parent_window = reinterpret_cast<WindowHandle>(parent_window_id);
  config.fxmy_main_window_rect = fxmy_main_window_rect;
  config.gen_task_id_callback = &OnGenTaskID;
  config.device_enumerated_callback = &OnDeviceFound;
  config.fxmy_state_changed_callback = &OnFxmyStateChanged;

  return fxmy_init(&config);
}

void SetStatusText(const std::wstring &text, bool error) {
#if USE_QT
#else
  SetWindowText(g_status_window, text.c_str());
  if (!IsWindowVisible(g_status_window)) {
    ShowWindow(g_status_window, SW_SHOW);
  }

  auto view_data = reinterpret_cast<ViewData *>(
      GetWindowLongPtr(g_status_window, GWLP_USERDATA));
  if (!view_data) {
    // create the view data
    view_data = new ViewData();
    SetWindowLongPtr(g_status_window, GWLP_USERDATA,
                     reinterpret_cast<LONG_PTR>(view_data));
  }

  view_data->text_color = error ? RGB(255, 0, 0) : RGB(0, 0, 0);
  InvalidateRect(g_status_window, NULL, TRUE);
#endif
}

#if !USE_QT

LRESULT WINAPI StatusWindowProc(HWND hwnd, UINT uMsg, WPARAM wParam,
                                LPARAM lParam) {
  switch (uMsg) {
  case WM_NCPAINT: {
    return 0;
  }
  case WM_PAINT: {
    // draw the window text in the center and draw a frame in the window
    PAINTSTRUCT ps;
    HDC hdc = BeginPaint(hwnd, &ps);
    RECT rect;
    GetClientRect(hwnd, &rect);
    // fill the window with white color
    HBRUSH hBrush = CreateSolidBrush(RGB(255, 255, 255));
    TCHAR text[256] = {0};
    GetWindowText(hwnd, text, 256);

    FillRect(hdc, &rect, hBrush);

    auto view_data = reinterpret_cast<ViewData *>(
        GetWindowLongPtr(g_status_window, GWLP_USERDATA));
    HFONT old_font = NULL;
    // set font size
    if (g_status_font) {
      old_font = (HFONT)SelectObject(hdc, g_status_font);
      SetBkMode(hdc, TRANSPARENT);
      if (view_data) {
        SetTextColor(hdc, view_data->text_color);
      } else {
        SetTextColor(hdc, RGB(0, 0, 0));
      }
    }

    DrawText(hdc, text, -1, &rect, DT_SINGLELINE | DT_CENTER | DT_VCENTER);
    if (old_font) {
      SelectObject(hdc, old_font);
    }

    DeleteObject(hBrush);
    EndPaint(hwnd, &ps);
    return 0;
    break;
  }
  case WM_ERASEBKGND: {
    return 1;
  }
  default:
    if (g_old_status_wnd_proc) {
      return CallWindowProc(g_old_status_wnd_proc, hwnd, uMsg, wParam, lParam);
    } else {
      return DefWindowProc(hwnd, uMsg, wParam, lParam);
    }
  }
  return 0;
}

// create a window to show the UI
LRESULT WINAPI WindowProc(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam) {
  switch (uMsg) {
  case WM_DESTROY:
    fxmy_uninit();
    PostQuitMessage(0);
    break;
  case WM_CREATE: {
    break;
  }
  case WM_LBUTTONDOWN: {
    std::cout << "left button down" << std::endl;
    break;
  }
  case WM_LBUTTONUP: {
    std::cout << "left button up" << std::endl;
    break;
  }
  case WM_PAINT: {
    PAINTSTRUCT ps;
    HDC hdc = BeginPaint(hwnd, &ps);
    // create red color brush
    HBRUSH hBrush = CreateSolidBrush(RGB(27, 48, 95));
    HWND fxmy_main_window = reinterpret_cast<HWND>(fxmy_get_main_window());
    HRGN hRgn = NULL, hRgn2 = NULL;
    RECT rect;
    memset(&rect, 0, sizeof(rect));
    GetClientRect(hwnd, &rect);

    if (fxmy_main_window) {
      // set clip region to exclude the fxmy main window
      RECT fxmy_main_window_rect;
      memset(&fxmy_main_window_rect, 0, sizeof(fxmy_main_window_rect));
      GetWindowRect(fxmy_main_window, &fxmy_main_window_rect);

      // map the fxmy main window rect to the current window
      MapWindowPoints(HWND_DESKTOP, hwnd,
                      reinterpret_cast<LPPOINT>(&fxmy_main_window_rect), 2);

      NormalizeRect(rect);
      hRgn = CreateRectRgn(rect.left, rect.top, rect.right, rect.bottom);
      hRgn2 = CreateRectRgn(
          fxmy_main_window_rect.left, fxmy_main_window_rect.top,
          fxmy_main_window_rect.right, fxmy_main_window_rect.bottom);
      CombineRgn(hRgn, hRgn, hRgn2, RGN_DIFF);
      SelectClipRgn(hdc, hRgn);
    }

    FillRect(hdc, &rect, hBrush);
    EndPaint(hwnd, &ps);
    DeleteObject(hBrush);
    if (hRgn) {
      DeleteObject(hRgn);
    }

    if (hRgn2) {
      DeleteObject(hRgn2);
    }

    // need send the paint message to the fxmy main window to repaint
    fxmy_main_window_flush();
    break;
  }
  case WM_SIZE: {
    // resize the fxmy main window to the size of the current window
    HWND fxmy_main_window = reinterpret_cast<HWND>(fxmy_get_main_window());
    if (fxmy_main_window) {
      LayoutFxmyMainWindow(hwnd, fxmy_main_window);
    }

    LayoutHeaderView(g_static_window, g_main_window);
    LayoutStatusView(g_status_window, g_main_window);
    break;
  }
  case WM_COMMAND: {
    if (HIWORD(wParam) == BN_CLICKED) {
      if (LOWORD(wParam) == ID_BTN_CAPTURE_ALL) {
        SetEnable(ID_BTN_CAPTURE_ALL, g_static_window, false);
        // means the capture all button clicked
        auto ret = fxmy_capture_start();
        if (ret != STATUS_OK) {
          MessageBox(hwnd, L"采集失败！", L"提示", MB_OK);
        }

        SetEnable(ID_BTN_CAPTURE_ALL, g_static_window, true);
      } else if (LOWORD(wParam) == ID_BTN_CAPTURE_CANCEL) {
        // means the cancel button clicked
        SetEnable(ID_BTN_CAPTURE_ALL, g_static_window, false);
        auto ret = fxmy_capture_stop();
        if (ret != STATUS_OK) {
          MessageBox(hwnd, L"停止采集失败！", L"提示", MB_OK);
        }

        SetEnable(ID_BTN_CAPTURE_ALL, g_static_window, true);
      }
    }
    break;
  }
  case WM_USER_RUN_ON_UI_THREAD: {
    auto func = reinterpret_cast<std::function<void()> *>(wParam);
    (*func)();
    if (lParam) {
      auto promise = reinterpret_cast<std::promise<void> *>(lParam);
      promise->set_value();
    }

    delete func;
    break;
  }
  default:
    return DefWindowProc(hwnd, uMsg, wParam, lParam);
  }
  return 0;
}

LRESULT CALLBACK StaticWindowProc(HWND hwnd, UINT msg, WPARAM wParam,
                                  LPARAM lParam) {
  switch (msg) {
  case WM_COMMAND: {
    // route the message to the parent window
    SendMessage(GetParent(hwnd), msg, wParam, lParam);
  }
  default:
    if (g_old_static_wnd_proc) {
      return CallWindowProc(g_old_static_wnd_proc, hwnd, msg, wParam, lParam);
    } else {
      return DefWindowProc(hwnd, msg, wParam, lParam);
    }
  }

  return 0;
}

HWND CreateMainWindow() {
  WNDCLASS wc = {0};
  wc.lpfnWndProc = WindowProc;
  wc.hInstance = GetModuleHandle(NULL);
  wc.lpszClassName = L"Aiwuu";
  // can resize the window
  wc.style = CS_HREDRAW | CS_VREDRAW;
  wc.hCursor = LoadCursor(NULL, IDC_ARROW);
  RegisterClass(&wc);

  return CreateWindow(wc.lpszClassName, L"Aiwuu", WS_OVERLAPPEDWINDOW,
                      CW_USEDEFAULT, CW_USEDEFAULT, CW_USEDEFAULT,
                      CW_USEDEFAULT, NULL, NULL, wc.hInstance, NULL);
}

// initialize the header view
bool CreateHeaderView(HWND parent) {
  HWND hwnd = CreateWindow(L"STATIC", L"", WS_CHILD | WS_VISIBLE, 0, 0, 0, 0,
                           parent, NULL, NULL, NULL);
  if (!hwnd) {
    return false;
  }

  g_static_window = hwnd;
  g_old_static_wnd_proc =
      (WNDPROC)SetWindowLongPtr(hwnd, GWLP_WNDPROC, (LONG_PTR)StaticWindowProc);

  const int kDefaultButtonWidth = 100;
  const int kDefaultButtonHeight = 30;

  // create the cancel button
  HWND btn_cancel = CreateWindow(
      L"BUTTON", L"取消采集", WS_CHILD | WS_VISIBLE | BS_PUSHBUTTON, 0, 0,
      kDefaultButtonWidth, kDefaultButtonHeight, hwnd,
      (HMENU)ID_BTN_CAPTURE_CANCEL, NULL, NULL);
  if (!btn_cancel) {
    return false;
  }

  // create the button capture all
  HWND btn_capture_all = CreateWindow(
      L"BUTTON", L"采集全部", WS_CHILD | WS_VISIBLE | BS_PUSHBUTTON, 0, 0,
      kDefaultButtonWidth, kDefaultButtonHeight, hwnd,
      (HMENU)ID_BTN_CAPTURE_ALL, NULL, NULL);
  if (!btn_capture_all) {
    return false;
  }

  return true;
}

// create a window to show the status
bool CreateStatusWindow(HWND parent, bool visible = true) {
  HWND hwnd = CreateWindow(L"STATIC", L"", WS_CHILD | WS_VISIBLE, 0, 0, 0, 0,
                           parent, NULL, NULL, NULL);
  if (!hwnd) {
    return false;
  }

  g_status_window = hwnd;
  g_old_status_wnd_proc =
      (WNDPROC)SetWindowLongPtr(hwnd, GWLP_WNDPROC, (LONG_PTR)StatusWindowProc);

  g_status_font =
      CreateFont(50, 0, 0, 0, FW_DONTCARE, FALSE, FALSE, FALSE, DEFAULT_CHARSET,
                 OUT_OUTLINE_PRECIS, CLIP_DEFAULT_PRECIS, CLEARTYPE_QUALITY,
                 VARIABLE_PITCH, TEXT("Arial"));
  if (!g_status_font) {
    return false;
  }

  SetStatusText(L"Aiwuu智能教育系统未启动");
  return true;
}

int main(int argc, char *argv[]) {
  Version version;
  memset(&version, 0, sizeof(version));
  fxmy_get_version(&version);
  std::cout << "aiwuu version: " << version.major << "." << version.minor << "."
            << version.revision << std::endl;

  // create a window to show the UI
  g_main_window = CreateMainWindow();
  // set window title
  SetWindowText(g_main_window, L"Aiwuu for Win32(C++)");
  if (!CreateHeaderView(g_main_window)) {
    MessageBox(g_main_window, L"创建头部视图失败！", L"提示", MB_OK);
    return -1;
  }

  if (!CreateStatusWindow(g_main_window, false)) {
    MessageBox(g_main_window, L"创建状态视图失败！", L"提示", MB_OK);
    return -1;
  }

  ShowWindow(g_main_window, SW_SHOWMAXIMIZED);

  auto fxmy_main_window_rect =
      CalcFxmyMainWindowRect(g_main_window, g_static_window);

  Rect rect = {fxmy_main_window_rect.left, fxmy_main_window_rect.top,
               fxmy_main_window_rect.right - fxmy_main_window_rect.left,
               fxmy_main_window_rect.bottom - fxmy_main_window_rect.top};
  // initialize the fxmy library
  auto status =
      InitializeFxmyUI(g_main_window, UI_TYPE_WIN32, argc, argv, rect);
  if (status != STATUS_OK) {
    MessageBox(g_main_window, L"Aiwuu智能教育系统初始化失败！", L"提示", MB_OK);
    return -1;
  }

  MSG msg;
  while (GetMessage(&msg, NULL, 0, 0)) {
    TranslateMessage(&msg);
    DispatchMessage(&msg);
  }

  return 0;
}

#else

class FxmyMainWindowView : public QFrame {
public:
  FxmyMainWindowView(QWidget *parent) : QFrame(parent) {
    setObjectName("aiwuu_main_window");
  }

  virtual void paintEvent(QPaintEvent *event) override {
    QPainter painter(this);
    painter.fillRect(rect(), QColor(0, 8, 95));
  }

  virtual void resizeEvent(QResizeEvent *event) override {
    // resize the fxmy main window to the size of the current window
    HWND fxmy_main_window = reinterpret_cast<HWND>(fxmy_get_main_window());
    if (fxmy_main_window) {
      LayoutFxmyMainWindow(reinterpret_cast<HWND>(winId()), fxmy_main_window);
    }
  }
};

int main(int argc, char *argv[]) {
  QApplication app(argc, argv);
  QFrame window;
  window.showMaximized();

  window.setWindowTitle(QStringLiteral("Aiwuu for QT(C++)"));

  QVBoxLayout *layout = new QVBoxLayout();
  window.setLayout(layout);

  FxmyMainWindowView *content_view = new FxmyMainWindowView(&window);
  layout->addWidget(content_view);

  QPushButton *btn_capture_all =
      new QPushButton(QStringLiteral("采集全部"), &window);
  QPushButton *btn_capture_cancel =
      new QPushButton(QStringLiteral("取消采集"), &window);
  QHBoxLayout *btn_layout = new QHBoxLayout();
  btn_layout->addStretch();
  btn_layout->addWidget(btn_capture_all);
  btn_layout->addWidget(btn_capture_cancel);
  btn_layout->addStretch();
  layout->addLayout(btn_layout);

  QObject::connect(btn_capture_all, &QPushButton::clicked, [&]() {
    btn_capture_all->setEnabled(false);
    auto ret = fxmy_capture_start();
    if (ret != STATUS_OK) {
      QMessageBox::information(&window, "提示", "采集失败！");
    }
    btn_capture_all->setEnabled(true);
  });

  QObject::connect(btn_capture_cancel, &QPushButton::clicked, [&]() {
    btn_capture_cancel->setEnabled(false);
    auto ret = fxmy_capture_stop();
    if (ret != STATUS_OK) {
      QMessageBox::information(&window, "提示", "停止采集失败！");
    }
    btn_capture_cancel->setEnabled(true);
  });

  const int fxmy_main_window_width = 800;
  const int fxmy_main_window_height = 600;
  // make fxmy main window center of the parent window
  QRect rect = window.geometry();
  Rect fxmy_main_window_rect = {(rect.width() - fxmy_main_window_width) / 2,
                                (rect.height() - fxmy_main_window_height) / 2,
                                fxmy_main_window_width,
                                fxmy_main_window_height};
  auto status = InitializeFxmyUI(reinterpret_cast<HWND>(content_view->winId()),
                                 UI_TYPE_QT, argc, argv, fxmy_main_window_rect);
  if (status != STATUS_OK) {
    QMessageBox::information(&window, "提示", "Aiwuu智能教育系统初始化失败！");
    return -1;
  }

  int ret = app.exec();
  fxmy_uninit();
  return ret;
}

#endif