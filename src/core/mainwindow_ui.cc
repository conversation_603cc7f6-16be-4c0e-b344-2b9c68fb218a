#include "core/mainwindow_ui.h"
#include "api/api.h"
#include "core/capture_ui.h"
#include "core/loginwindow_ui.h"
#include "ui_mainwindow.h"
#include "utils/logger.h"
#include "utils/storage.h"
#include "utils/toast.h"

#include <QApplication>
#include <QDateTime>
#include <QDebug>
#include <QNetworkReply>
#include <QScreen>
#include <QStyleOption>

MainWindowUI::MainWindowUI(QWidget *parent)
    : QMainWindow(parent), ui(new Ui::MainWindow), timer(nullptr) {
  ui->setupUi(this);

  // 设置窗口属性
  setWindowTitle(APP_NAME);
  resize(WINDOW_WIDTH, WINDOW_HEIGHT);

  // 设置UI
  setupUI();
  connectSignals();

  // 设置定时器更新时间
  timer = new QTimer(this);
  connect(timer, &QTimer::timeout, this, &MainWindowUI::updateTime);
  timer->start(1000);
  updateTime();

  Logger::getInstance().log(Logger::Info, "MainWindow UI 初始化完成");
}

MainWindowUI::~MainWindowUI() {
  delete ui;

  if (timer) {
    timer->stop();
  }

  // 清理动画
  for (auto animation : animations) {
    animation->stop();
    delete animation;
  }
  animations.clear();
}

void MainWindowUI::setupUI() {
  // 设置初始状态
  updateStatus("就绪");

  // 设置各个部分
  setupHeader();
  setupMainContent();
  setupFooter();

  // 设置动画
  setupAnimations();
}

void MainWindowUI::paintEvent(QPaintEvent *event) {
  // 创建自定义背景
  QPainter painter(this);
  painter.setRenderHint(QPainter::Antialiasing);

  // 创建渐变背景
  QLinearGradient gradient(0, 0, width(), height());
  gradient.setColorAt(0, QColor(33, 150, 243));   // #2196f3
  gradient.setColorAt(0.5, QColor(66, 165, 245)); // #42a5f5
  gradient.setColorAt(1, QColor(100, 181, 246));  // #64b5f6

  painter.fillRect(rect(), gradient);

  // 绘制其他自定义组件
  QMainWindow::paintEvent(event);
}

void MainWindowUI::setupHeader() {
  // 创建顶部导航栏
  headerWidget = new QWidget(this);
  headerWidget->setObjectName("headerWidget");
  headerWidget->setFixedHeight(70);
  headerWidget->setGeometry(0, 0, width(), 70);

  // 设置样式
  headerWidget->setStyleSheet(
      "QWidget#headerWidget {"
      "  background-color: rgba(255, 255, 255, 0.1);"
      "  border-bottom: 1px solid rgba(255, 255, 255, 0.2);"
      "}");

  // 创建水平布局
  QHBoxLayout *headerLayout = new QHBoxLayout(headerWidget);
  headerLayout->setContentsMargins(24, 0, 24, 0);
  headerLayout->setSpacing(0);

  // Logo区域
  logoArea = new QWidget();
  QHBoxLayout *logoLayout = new QHBoxLayout(logoArea);
  logoLayout->setContentsMargins(0, 0, 0, 0);
  logoLayout->setSpacing(12);

  logoIcon = new QLabel();
  logoIcon->setFixedSize(40, 40);
  logoIcon->setStyleSheet("background-color: rgba(255, 255, 255, 0.2);"
                          "border-radius: 20px;"
                          "color: white;"
                          "font-size: 20px;"
                          "font-weight: bold;");
  logoIcon->setAlignment(Qt::AlignCenter);
  logoIcon->setText("AI"); // 简单使用文字替代图标

  logoText = new QLabel("智能开发平台");
  logoText->setStyleSheet("color: white;"
                          "font-size: 20px;"
                          "font-weight: bold;"
                          "letter-spacing: 1px;");

  logoLayout->addWidget(logoIcon);
  logoLayout->addWidget(logoText);

  // 导航菜单
  navMenu = new QWidget();
  QHBoxLayout *navLayout = new QHBoxLayout(navMenu);
  navLayout->setContentsMargins(0, 0, 0, 0);
  navLayout->setSpacing(24);

  // 添加导航项
  QStringList navItems = {"首页", "功能中心", "数据分析", "系统设置",
                          "帮助文档"};
  for (const QString &item : navItems) {
    QPushButton *navButton = new QPushButton(item);
    navButton->setStyleSheet("QPushButton {"
                             "  color: white;"
                             "  background-color: transparent;"
                             "  border: none;"
                             "  padding: 8px 16px;"
                             "  font-size: 16px;"
                             "  border-radius: 6px;"
                             "}"
                             "QPushButton:hover {"
                             "  background-color: rgba(255, 255, 255, 0.2);"
                             "}");

    if (item == "首页") {
      navButton->setStyleSheet(navButton->styleSheet() +
                               "QPushButton {"
                               "  background-color: rgba(255, 255, 255, 0.2);"
                               "}");
    }

    navLayout->addWidget(navButton);
  }

  // 用户区域
  userArea = new QWidget();
  QHBoxLayout *userLayout = new QHBoxLayout(userArea);
  userLayout->setContentsMargins(0, 0, 0, 0);
  userLayout->setSpacing(16);

  // 搜索框
  searchBox = new QLineEdit();
  searchBox->setPlaceholderText("搜索功能或内容");
  searchBox->setFixedWidth(200);
  searchBox->setStyleSheet("QLineEdit {"
                           "  background-color: rgba(255, 255, 255, 0.1);"
                           "  border: none;"
                           "  border-radius: 20px;"
                           "  padding: 8px 16px 8px 36px;"
                           "  color: white;"
                           "}"
                           "QLineEdit::placeholder {"
                           "  color: rgba(255, 255, 255, 0.7);"
                           "}");

  // 添加搜索图标
  QLabel *searchIcon = new QLabel(searchBox);
  searchIcon->setText("🔍");
  searchIcon->setStyleSheet("color: white;");
  searchIcon->setGeometry(12, 8, 16, 16);

  // 通知按钮
  notificationButton = new QPushButton();
  notificationButton->setFixedSize(36, 36);
  notificationButton->setText("🔔");
  notificationButton->setStyleSheet(
      "QPushButton {"
      "  background-color: rgba(255, 255, 255, 0.1);"
      "  border-radius: 18px;"
      "  color: white;"
      "  font-size: 16px;"
      "}"
      "QPushButton:hover {"
      "  background-color: rgba(255, 255, 255, 0.2);"
      "}");

  // 用户头像按钮
  userAvatarButton = new QPushButton();
  userAvatarButton->setFixedSize(36, 36);
  userAvatarButton->setText("👤");
  userAvatarButton->setStyleSheet(
      "QPushButton {"
      "  background-color: rgba(255, 255, 255, 0.1);"
      "  border-radius: 18px;"
      "  color: white;"
      "  font-size: 16px;"
      "}"
      "QPushButton:hover {"
      "  background-color: rgba(255, 255, 255, 0.2);"
      "}");

  userLayout->addWidget(searchBox);
  userLayout->addWidget(notificationButton);
  userLayout->addWidget(userAvatarButton);

  // 将所有部件添加到头部布局
  headerLayout->addWidget(logoArea);
  headerLayout->addStretch();
  headerLayout->addWidget(navMenu);
  headerLayout->addStretch();
  headerLayout->addWidget(userArea);
}

void MainWindowUI::setupMainContent() {
  // 创建主内容区域
  contentArea = new QWidget(this);
  contentArea->setObjectName("contentArea");
  contentArea->setGeometry(0, 70, width(), height() - 120);

  QVBoxLayout *contentLayout = new QVBoxLayout(contentArea);
  contentLayout->setContentsMargins(40, 40, 40, 40);
  contentLayout->setSpacing(24);

  // 欢迎部分
  welcomeLabel = new QLabel("欢迎使用智能开发平台");
  welcomeLabel->setAlignment(Qt::AlignCenter);
  welcomeLabel->setStyleSheet("color: white;"
                              "font-size: 24px;"
                              "font-weight: bold;");

  // 日期标签
  QDateTime now = QDateTime::currentDateTime();
  QString dateStr = now.toString("yyyy-MM-dd, dddd");
  dateLabel = new QLabel(dateStr);
  dateLabel->setAlignment(Qt::AlignCenter);
  dateLabel->setStyleSheet("color: rgba(255, 255, 255, 0.8);"
                           "font-size: 14px;");

  contentLayout->addWidget(welcomeLabel);
  contentLayout->addWidget(dateLabel);

  // 设置中央AI区域和功能模块
  setupCentralAI();
  setupFunctionModules();

  // 添加到主布局
  contentLayout->addWidget(centralAiArea);
  contentLayout->addWidget(functionModules);
}

void MainWindowUI::setupCentralAI() {
  // 中央AI区域
  centralAiArea = new QWidget();
  centralAiArea->setFixedSize(400, 400);
  centralAiArea->setObjectName("centralAiArea");

  // 居中布局
  QVBoxLayout *centralLayout = new QVBoxLayout(contentArea);
  centralLayout->setAlignment(Qt::AlignCenter);
  centralLayout->addWidget(centralAiArea);

  // 创建同心圆
  aiCircleOuter = createCircleWidget(400, QColor(255, 255, 255, 40));
  aiCircleMiddle = createCircleWidget(300, QColor(255, 255, 255, 50));
  aiCircleInner = createCircleWidget(250, QColor(255, 255, 255, 60));

  // 机器人图像
  aiRobot = new QLabel();
  aiRobot->setFixedSize(200, 200);
  aiRobot->setStyleSheet("background-color: rgba(255, 255, 255, 0.15);"
                         "border-radius: 100px;");

  // 创建布局
  QVBoxLayout *outerLayout = new QVBoxLayout(aiCircleOuter);
  outerLayout->setContentsMargins(0, 0, 0, 0);
  outerLayout->setAlignment(Qt::AlignCenter);
  outerLayout->addWidget(aiCircleMiddle);

  QVBoxLayout *middleLayout = new QVBoxLayout(aiCircleMiddle);
  middleLayout->setContentsMargins(0, 0, 0, 0);
  middleLayout->setAlignment(Qt::AlignCenter);
  middleLayout->addWidget(aiCircleInner);

  QVBoxLayout *innerLayout = new QVBoxLayout(aiCircleInner);
  innerLayout->setContentsMargins(0, 0, 0, 0);
  innerLayout->setAlignment(Qt::AlignCenter);
  innerLayout->addWidget(aiRobot);

  // 设置机器人图像
  aiRobot->setText("AI");
  aiRobot->setAlignment(Qt::AlignCenter);
  aiRobot->setStyleSheet("background-color: rgba(64, 169, 255, 0.3);"
                         "border-radius: 100px;"
                         "color: white;"
                         "font-size: 80px;"
                         "font-weight: bold;");

  // 添加到中央AI区域
  QVBoxLayout *aiLayout = new QVBoxLayout(centralAiArea);
  aiLayout->setContentsMargins(0, 0, 0, 0);
  aiLayout->setAlignment(Qt::AlignCenter);
  aiLayout->addWidget(aiCircleOuter);
}

QWidget *MainWindowUI::createCircleWidget(int size, const QColor &color,
                                          qreal opacity) {
  QWidget *circle = new QWidget();
  circle->setFixedSize(size, size);
  circle->setStyleSheet(QString("background-color: %1;"
                                "border-radius: %2px;")
                            .arg(color.name(QColor::HexArgb))
                            .arg(size / 2));

  // 设置不透明度
  QGraphicsOpacityEffect *effect = new QGraphicsOpacityEffect(circle);
  effect->setOpacity(opacity);
  circle->setGraphicsEffect(effect);

  return circle;
}

void MainWindowUI::setupFunctionModules() {
  // 功能模块区域
  functionModules = new QWidget();
  functionModules->setObjectName("functionModules");
  functionModules->setMinimumHeight(600);

  // 使用绝对布局
  functionModules->setLayout(new QVBoxLayout());
  functionModules->layout()->setContentsMargins(0, 0, 0, 0);

  // 创建功能模块卡片
  QStringList icons = {"🧠", "📁", "🛒", "👥", "🛡️", "⚙️"};
  QStringList titles = {"AI人工智能平台",   "智慧档案管理平台",
                        "智慧云采购平台",   "企业人员管理平台",
                        "风险管控指挥中心", "AI系统设置平台"};
  QStringList subtitles = {"AI ARTIFICIAL INTELLIGENCE", "ARCHIVE MANAGEMENT",
                           "ZHIHUIYUN PROCUREMENT",      "ENTERPRISE PERSONNEL",
                           "RISK CONTROL COMMAND",       "AI SYSTEM SETTINGS"};
  QStringList colors = {"blue", "yellow", "red", "cyan", "green", "orange"};

  // 创建模块卡片
  for (int i = 0; i < titles.size(); i++) {
    QWidget *card =
        createModuleCard(icons[i], titles[i], subtitles[i], colors[i]);
    card->setParent(functionModules);
    moduleCards.append(card);
  }

  // 设置卡片位置
  QList<QPoint> positions = {
      QPoint(500, 60),  // 顶部中央
      QPoint(800, 200), // 右上
      QPoint(200, 200), // 左上
      QPoint(500, 500), // 底部中央
      QPoint(200, 350), // 左下
      QPoint(800, 350)  // 右下
  };

  for (int i = 0; i < moduleCards.size(); i++) {
    moduleCards[i]->move(positions[i]);
  }
}

QWidget *MainWindowUI::createModuleCard(const QString &iconClass,
                                        const QString &title,
                                        const QString &subtitle,
                                        const QString &colorClass) {
  QWidget *card = new QWidget();
  card->setObjectName("moduleCard");
  card->setFixedSize(280, 80);

  // 设置基本样式
  card->setStyleSheet("QWidget#moduleCard {"
                      "  background-color: rgba(255, 255, 255, 0.1);"
                      "  border-radius: 12px;"
                      "}"
                      "QWidget#moduleCard:hover {"
                      "  background-color: rgba(255, 255, 255, 0.15);"
                      "}");

  // 添加阴影效果
  QGraphicsDropShadowEffect *shadow = new QGraphicsDropShadowEffect(card);
  shadow->setBlurRadius(15);
  shadow->setColor(QColor(0, 0, 0, 50));
  shadow->setOffset(0, 5);
  card->setGraphicsEffect(shadow);

  // 创建布局
  QHBoxLayout *cardLayout = new QHBoxLayout(card);
  cardLayout->setContentsMargins(16, 16, 16, 16);
  cardLayout->setSpacing(16);

  // 创建图标
  QLabel *icon = new QLabel();
  icon->setFixedSize(60, 60);
  icon->setAlignment(Qt::AlignCenter);
  icon->setText(iconClass);
  icon->setStyleSheet(QString("border-radius: 12px;"
                              "font-size: 28px;"
                              "color: %1;"
                              "background-color: %2;")
                          .arg(getIconColor(colorClass))
                          .arg(getIconBackground(colorClass)));

  // 创建内容区域
  QWidget *content = new QWidget();
  QVBoxLayout *contentLayout = new QVBoxLayout(content);
  contentLayout->setContentsMargins(0, 0, 0, 0);
  contentLayout->setSpacing(4);

  QLabel *titleLabel = new QLabel(title);
  titleLabel->setStyleSheet("color: white;"
                            "font-size: 16px;"
                            "font-weight: bold;");

  QLabel *subtitleLabel = new QLabel(subtitle);
  subtitleLabel->setStyleSheet("color: rgba(255, 255, 255, 0.7);"
                               "font-size: 12px;");

  contentLayout->addWidget(titleLabel);
  contentLayout->addWidget(subtitleLabel);

  // 添加到卡片布局
  cardLayout->addWidget(icon);
  cardLayout->addWidget(content);

  // 设置卡片为可点击
  card->setCursor(Qt::PointingHandCursor);

  // 连接点击事件
  card->installEventFilter(this);

  return card;
}

QString MainWindowUI::getIconColor(const QString &colorClass) {
  if (colorClass == "blue")
    return "#40a9ff";
  if (colorClass == "yellow")
    return "#faad14";
  if (colorClass == "red")
    return "#f5222d";
  if (colorClass == "cyan")
    return "#13c2c2";
  if (colorClass == "green")
    return "#52c41a";
  if (colorClass == "orange")
    return "#fa8c16";
  return "#ffffff";
}

QString MainWindowUI::getIconBackground(const QString &colorClass) {
  if (colorClass == "blue")
    return "rgba(64, 169, 255, 0.3)";
  if (colorClass == "yellow")
    return "rgba(250, 173, 20, 0.3)";
  if (colorClass == "red")
    return "rgba(245, 34, 45, 0.3)";
  if (colorClass == "cyan")
    return "rgba(19, 194, 194, 0.3)";
  if (colorClass == "green")
    return "rgba(82, 196, 26, 0.3)";
  if (colorClass == "orange")
    return "rgba(250, 140, 22, 0.3)";
  return "rgba(255, 255, 255, 0.3)";
}

void MainWindowUI::setupFooter() {
  // 创建底部状态栏
  footerWidget = new QWidget(this);
  footerWidget->setObjectName("footerWidget");
  footerWidget->setFixedHeight(50);
  footerWidget->setGeometry(0, height() - 50, width(), 50);
  footerWidget->setStyleSheet(
      "QWidget#footerWidget {"
      "  background-color: rgba(255, 255, 255, 0.1);"
      "  border-top: 1px solid rgba(255, 255, 255, 0.2);"
      "}");

  // 创建水平布局
  QHBoxLayout *footerLayout = new QHBoxLayout(footerWidget);
  footerLayout->setContentsMargins(24, 0, 24, 0);
  footerLayout->setSpacing(0);

  // 系统信息
  QWidget *systemInfo = new QWidget();
  QHBoxLayout *systemLayout = new QHBoxLayout(systemInfo);
  systemLayout->setContentsMargins(0, 0, 0, 0);
  systemLayout->setSpacing(10);

  systemVersionLabel = new QLabel("系统版本: v3.5.2");
  systemVersionLabel->setStyleSheet("color: rgba(255, 255, 255, 0.8);"
                                    "font-size: 14px;");

  QLabel *separator = new QLabel("|");
  separator->setStyleSheet("color: rgba(255, 255, 255, 0.5);"
                           "font-size: 14px;");

  systemStatusLabel = new QLabel("运行状态: 正常");
  systemStatusLabel->setStyleSheet("color: rgba(255, 255, 255, 0.8);"
                                   "font-size: 14px;");

  systemLayout->addWidget(systemVersionLabel);
  systemLayout->addWidget(separator);
  systemLayout->addWidget(systemStatusLabel);

  // 版权信息
  copyrightLabel = new QLabel("© 2025 智能开发平台 - 保留所有权利");
  copyrightLabel->setStyleSheet("color: rgba(255, 255, 255, 0.8);"
                                "font-size: 14px;");

  // 时间信息
  timeLabel = new QLabel();
  timeLabel->setStyleSheet("color: rgba(255, 255, 255, 0.8);"
                           "font-size: 14px;");

  // 添加到底部布局
  footerLayout->addWidget(systemInfo);
  footerLayout->addStretch();
  footerLayout->addWidget(copyrightLabel);
  footerLayout->addStretch();
  footerLayout->addWidget(timeLabel);
}

void MainWindowUI::setupAnimations() {
  // 为功能模块卡片添加浮动动画
  for (int i = 0; i < moduleCards.size(); i++) {
    QPropertyAnimation *animation =
        new QPropertyAnimation(moduleCards[i], "pos");
    animation->setDuration(3000 + i * 500); // 错开动画时间
    animation->setStartValue(moduleCards[i]->pos());
    animation->setEndValue(moduleCards[i]->pos() + QPoint(0, -10));
    animation->setEasingCurve(QEasingCurve::InOutQuad);
    animation->setLoopCount(-1); // 无限循环
    animation->setDirection(QPropertyAnimation::Forward);
    animation->start();

    animations.append(animation);
  }

  // 为AI圆圈添加脉动动画
  QPropertyAnimation *pulseAnimation =
      new QPropertyAnimation(aiCircleOuter, "geometry");
  pulseAnimation->setDuration(2000);
  pulseAnimation->setStartValue(QRect(aiCircleOuter->x(), aiCircleOuter->y(),
                                      aiCircleOuter->width(),
                                      aiCircleOuter->height()));
  pulseAnimation->setEndValue(
      QRect(aiCircleOuter->x() - 10, aiCircleOuter->y() - 10,
            aiCircleOuter->width() + 20, aiCircleOuter->height() + 20));
  pulseAnimation->setEasingCurve(QEasingCurve::InOutQuad);
  pulseAnimation->setLoopCount(-1);
  pulseAnimation->setDirection(QPropertyAnimation::Forward);
  pulseAnimation->start();

  animations.append(pulseAnimation);
}

void MainWindowUI::connectSignals() {
  // 连接退出登录按钮
  connect(ui->logoutButton, &QPushButton::clicked, this,
          &MainWindowUI::onLogoutClicked);

  // 连接功能按钮
  connect(ui->collectButton, &QPushButton::clicked, this,
          &MainWindowUI::onCollectClicked);
  connect(ui->courseButton, &QPushButton::clicked, this,
          &MainWindowUI::onCourseButtonClicked);
  connect(ui->studentButton, &QPushButton::clicked, this,
          &MainWindowUI::onStudentButtonClicked);
  connect(ui->settingsButton, &QPushButton::clicked, this,
          &MainWindowUI::onSettingsButtonClicked);

  // 连接模块卡片点击事件
  for (QWidget *card : moduleCards) {
    card->installEventFilter(this);
  }
}

void MainWindowUI::updateTime() {
  QDateTime now = QDateTime::currentDateTime();
  QString timeStr = now.toString("hh:mm:ss");
  timeLabel->setText("当前时间: " + timeStr);
}

void MainWindowUI::showEvent(QShowEvent *event) {
  QMainWindow::showEvent(event);
  Logger::getInstance().log(Logger::Debug, "MainWindow UI 显示");
  updateStatus("主页面已加载");
}

void MainWindowUI::closeEvent(QCloseEvent *event) {
  Logger::getInstance().log(Logger::Debug, "MainWindow UI 关闭，清理资源...");
  QMainWindow::closeEvent(event);
}

void MainWindowUI::updateStatus(const QString &status) {
  ui->statusLabel->setText(status);
  Logger::getInstance().log(Logger::Debug, QString("状态更新: %1").arg(status));
}

void MainWindowUI::onLogoutClicked() {
  Logger::getInstance().log(Logger::Info, "用户点击退出登录");
  updateStatus("正在退出登录...");

  // 使用NetworkManager的回调机制
  NetworkManager::getInstance().post(
      Api::LOGOUT, QJsonObject(), QMap<QString, QString>(), // queryParams
      QMap<QString, QString>(),                             // pathParams
      [=](const QJsonObject &response) {
        // 成功回调
        Logger::getInstance().log(Logger::Info, "退出登录成功");
        Storage::getInstance().clear();

        // 清除认证token
        NetworkManager::getInstance().setAuthToken("");

        // 创建登录窗口
        LoginWindowUI *loginWindow = new LoginWindowUI();
        loginWindow->show();

        // 关闭主窗口
        this->close();
      },
      [=](const QString &error) {
        // 错误回调
        Logger::getInstance().log(Logger::Warning,
                                  QString("退出登录失败: %1").arg(error));
        Toast::error("退出登录失败", this);
        updateStatus("退出登录失败");
      },
      true); // requireAuth
}

void MainWindowUI::onCollectClicked() {
  Logger::getInstance().log(Logger::Info, "用户点击采集");
  updateStatus("正在打开采集页面...");

  CaptureUI *captureWindow = new CaptureUI();
  connect(captureWindow, &CaptureUI::backToMainWindow, [=]() {
    captureWindow->close();
    this->show();
    updateStatus("已返回主页面");
  });
  captureWindow->show();
  this->hide();
}

void MainWindowUI::onCourseButtonClicked() {
  Logger::getInstance().log(Logger::Info, "用户点击课程管理");
  updateStatus("课程管理功能开发中...");
  Toast::info("课程管理功能开发中...", this);
}

void MainWindowUI::onStudentButtonClicked() {
  Logger::getInstance().log(Logger::Info, "用户点击学生管理");
  updateStatus("学生管理功能开发中...");
  Toast::info("学生管理功能开发中...", this);
}

void MainWindowUI::onSettingsButtonClicked() {
  Logger::getInstance().log(Logger::Info, "用户点击系统设置");
  updateStatus("系统设置功能开发中...");
  Toast::info("系统设置功能开发中...", this);
}

bool MainWindowUI::eventFilter(QObject *watched, QEvent *event) {
  if (event->type() == QEvent::MouseButtonPress) {
    for (QWidget *card : moduleCards) {
      if (watched == card) {
        // 获取卡片标题
        QLayout *layout = card->layout();
        if (layout) {
          QLayoutItem *item = layout->itemAt(1);
          if (item && item->widget()) {
            QWidget *content = item->widget();
            if (content && content->layout() && content->layout()->itemAt(0) &&
                content->layout()->itemAt(0)->widget()) {
              QLabel *titleLabel = qobject_cast<QLabel *>(
                  content->layout()->itemAt(0)->widget());
              if (titleLabel) {
                qDebug() << "Module clicked:" << titleLabel->text();

                // 处理模块点击
                QString title = titleLabel->text();
                if (title == "AI人工智能平台") {
                  // 处理AI平台点击
                  Toast::info("AI人工智能平台功能开发中...", this);
                } else if (title == "智慧档案管理平台") {
                  // 处理档案管理点击
                  Toast::info("智慧档案管理平台功能开发中...", this);
                } else if (title == "智慧云采购平台") {
                  // 处理云采购点击
                  Toast::info("智慧云采购平台功能开发中...", this);
                } else if (title == "企业人员管理平台") {
                  // 处理人员管理点击
                  Toast::info("企业人员管理平台功能开发中...", this);
                } else if (title == "风险管控指挥中心") {
                  // 处理风险管控点击
                  Toast::info("风险管控指挥中心功能开发中...", this);
                } else if (title == "AI系统设置平台") {
                  // 处理系统设置点击
                  Toast::info("AI系统设置平台功能开发中...", this);
                }
              }
            }
          }
        }
        return true;
      }
    }
  }
  return QMainWindow::eventFilter(watched, event);
}