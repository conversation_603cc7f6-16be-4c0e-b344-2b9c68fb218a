#include "core/loginwindow.h"
#include "api/api.h"
#include "core/config.h"
#include "core/mainwindow.h"
#include "utils/formutils.h"
#include "utils/logger.h"
#include "utils/toast.h"
#include <QApplication>
#include <QGuiApplication>
#include <QScreen>

#include <QCheckBox>
#include <QDebug>
#include <QFile>
#include <QHBoxLayout>
#include <QJsonDocument>
#include <QJsonObject>
#include <QLabel>
#include <QLineEdit>
#include <QMouseEvent>
#include <QPainter>
#include <QPainterPath>
#include <QPushButton>
#include <QSizePolicy>
#include <QTimer>
#include <QVBoxLayout>

LoginWindow::LoginWindow(QWidget *parent) : QWidget(parent) {
  // 设置窗口属性
  // 设置窗口大小为固定尺寸，更适合登录界面
  int windowWidth = 1000;
  int windowHeight = 600;
  resize(windowWidth, windowHeight);

  // 窗口居中显示
  centerWindow();

  setWindowFlags(Qt::FramelessWindowHint);
  setAttribute(Qt::WA_TranslucentBackground);
  setAttribute(Qt::WA_NoSystemBackground);

  // 加载样式表
  loadStyleSheet();

  // 创建主容器
  QWidget *container = new QWidget(this);
  container->setObjectName("loginContainer");

  // 创建主布局
  QVBoxLayout *mainLayout = new QVBoxLayout(this);
  mainLayout->setContentsMargins(20, 20, 20, 20);
  mainLayout->setSpacing(0);
  mainLayout->addWidget(container);

  // 容器布局
  QVBoxLayout *containerLayout = new QVBoxLayout(container);
  containerLayout->setContentsMargins(0, 0, 0, 0);
  containerLayout->setSpacing(0);

  // 创建窗口控制按钮容器 - 使用绝对定位 (类似前端 position: absolute)
  QWidget *controlButtons = new QWidget(container);
  controlButtons->setFixedSize(100, 40);
  controlButtons->setObjectName("controlButtons");

  // 设置绝对定位：top: 10px, right: 20px
  // 初始位置会在resizeEvent中正确设置
  controlButtons->move(0, 0);

  // 确保按钮组在最上层显示
  controlButtons->raise();

  QHBoxLayout *controlLayout = new QHBoxLayout(controlButtons);
  controlLayout->setContentsMargins(0, 0, 0, 0);
  controlLayout->setSpacing(8);

  // 添加窗口控制按钮
  QPushButton *closeButton = new QPushButton(controlButtons);
  closeButton->setFixedSize(12, 12);
  closeButton->setObjectName("closeButton");
  closeButton->setCursor(Qt::PointingHandCursor);
  closeButton->setToolTip("关闭");
  closeButton->setText("×");

  QPushButton *minButton = new QPushButton(controlButtons);
  minButton->setFixedSize(12, 12);
  minButton->setObjectName("minButton");
  minButton->setCursor(Qt::PointingHandCursor);
  minButton->setToolTip("最小化");
  minButton->setText("−");

  QPushButton *maxButton = new QPushButton(controlButtons);
  maxButton->setFixedSize(12, 12);
  maxButton->setObjectName("maxButton");
  maxButton->setCursor(Qt::PointingHandCursor);
  maxButton->setToolTip("最大化");
  maxButton->setText("□");

  // macOS风格：从左到右排列为关闭、最小化、最大化
  controlLayout->addWidget(closeButton);
  controlLayout->addWidget(minButton);
  controlLayout->addWidget(maxButton);

  // 保存按钮组引用，用于窗口大小改变时重新定位
  m_controlButtons = controlButtons;

  // 创建内容容器
  QWidget *contentContainer = new QWidget(container);
  QHBoxLayout *contentLayout = new QHBoxLayout(contentContainer);
  contentLayout->setContentsMargins(0, 0, 0, 0);
  contentLayout->setSpacing(0);
  containerLayout->addWidget(contentContainer);

  // 左侧图片面板
  QWidget *leftPanel = new QWidget(contentContainer);
  leftPanel->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);
  leftPanel->setObjectName("leftPanel");
  leftPanel->setMinimumWidth(300); // 设置最小宽度
  leftPanel->setMaximumWidth(600); // 设置最大宽度，防止溢出

  QVBoxLayout *leftLayout = new QVBoxLayout(leftPanel);
  leftLayout->setContentsMargins(0, 0, 0, 0);
  leftLayout->setSpacing(0);

  // 添加图片标签
  QLabel *imageLabel = new QLabel(leftPanel);
  imageLabel->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);
  imageLabel->setAlignment(Qt::AlignCenter);
  imageLabel->setScaledContents(false); // 不使用自动缩放，我们手动控制
  imageLabel->setMinimumSize(300, 200); // 设置最小尺寸
  imageLabel->setMaximumWidth(600);     // 设置最大宽度，防止溢出

  QPixmap bgImage(":/images/login-bg.png");
  if (!bgImage.isNull()) {
    Logger::getInstance().log(Logger::Debug,
                              "Successfully loaded login background image");
    // 初始时先设置原图，在resizeEvent中进行正确的缩放
    imageLabel->setPixmap(bgImage);
    imageLabel->setStyleSheet(
        "border-top-left-radius: 8px; border-bottom-left-radius: 8px;");
  } else {
    Logger::getInstance().log(Logger::Warning,
                              "Failed to load login background image from "
                              "path: :/images/login-bg.png");
  }
  leftLayout->addWidget(imageLabel);
  contentLayout->addWidget(leftPanel, 1); // 设置比例为1

  // 右侧登录面板
  QWidget *rightPanel = new QWidget(contentContainer);
  rightPanel->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);
  rightPanel->setObjectName("rightPanel");
  rightPanel->setMinimumWidth(450); // 设置最小宽度

  // 右侧登录表单
  QVBoxLayout *formLayout = new QVBoxLayout(rightPanel);
  formLayout->setContentsMargins(60, 60, 60, 60);
  formLayout->setSpacing(0); // 设置为0，手动控制间距

  // 添加顶部弹性间距，实现垂直居中
  formLayout->addStretch(1);

  // 标题容器
  QWidget *titleContainer = new QWidget(rightPanel);
  titleContainer->setObjectName("titleContainer");
  QVBoxLayout *titleLayout = new QVBoxLayout(titleContainer);
  titleLayout->setContentsMargins(0, 0, 0, 0);
  titleLayout->setSpacing(6);

  // 标题
  QLabel *titleLabel = new QLabel(LOGIN_TITLE, titleContainer);
  titleLabel->setObjectName("titleLabel");
  titleLayout->addWidget(titleLabel, 0, Qt::AlignHCenter);

  formLayout->addWidget(titleContainer);

  // 标题和表单间距
  formLayout->addSpacing(8);

  // 创建表单内容容器
  QWidget *formContentContainer = new QWidget(rightPanel);
  formContentContainer->setObjectName("formContentContainer");
  QVBoxLayout *formContentLayout = new QVBoxLayout(formContentContainer);
  formContentLayout->setContentsMargins(0, 0, 0, 0);
  formContentLayout->setSpacing(0);

  // 用户名输入框容器
  QWidget *usernameContainer = new QWidget(formContentContainer);
  usernameContainer->setObjectName("usernameContainer");
  QVBoxLayout *usernameLayout = new QVBoxLayout(usernameContainer);
  usernameLayout->setContentsMargins(0, 0, 0, 0);
  usernameLayout->setSpacing(4); // label和输入框间距小点

  QLabel *usernameLabel = new QLabel("用户名", usernameContainer);
  usernameLabel->setObjectName("usernameLabel");
  usernameLayout->addWidget(usernameLabel);

  usernameEdit = new QLineEdit(usernameContainer);
  usernameEdit->setPlaceholderText("请输入用户名");
  usernameEdit->setFixedHeight(50);
  usernameLayout->addWidget(usernameEdit);
  formContentLayout->addWidget(usernameContainer);

  // 用户名输入框和密码label间距小点
  formContentLayout->addSpacing(6);

  // 密码输入框容器
  QWidget *passwordContainer = new QWidget(formContentContainer);
  passwordContainer->setObjectName("passwordContainer");
  QVBoxLayout *passwordLayout = new QVBoxLayout(passwordContainer);
  passwordLayout->setContentsMargins(0, 0, 0, 0);
  passwordLayout->setSpacing(4); // label和输入框间距小点

  QLabel *passwordLabel = new QLabel("密码", passwordContainer);
  passwordLabel->setObjectName("passwordLabel");
  passwordLayout->addWidget(passwordLabel);

  passwordEdit = new QLineEdit(passwordContainer);
  passwordEdit->setPlaceholderText("请输入密码");
  passwordEdit->setEchoMode(QLineEdit::Password);
  passwordEdit->setFixedHeight(50);
  passwordLayout->addWidget(passwordEdit);
  formContentLayout->addWidget(passwordContainer);

  // 密码输入框和登录按钮间距大点
  formContentLayout->addSpacing(40);

  // 登录按钮
  loginButton = new QPushButton("登录", formContentContainer);
  loginButton->setObjectName("loginButton");
  loginButton->setFixedHeight(45);
  loginButton->setCursor(Qt::PointingHandCursor);
  formContentLayout->addWidget(loginButton);

  // 登录按钮和忘记密码间距小点
  formContentLayout->addSpacing(6);

  // 添加忘记密码按钮
  QPushButton *forgotButton = new QPushButton("忘记密码", formContentContainer);
  forgotButton->setObjectName("forgotButton");
  forgotButton->setCursor(Qt::PointingHandCursor);
  formContentLayout->addWidget(forgotButton, 0, Qt::AlignRight);

  // 将表单内容容器添加到主表单布局
  formLayout->addWidget(formContentContainer);

  // 添加底部弹性间距，将表单内容推向上方
  formLayout->addStretch(1);

  contentLayout->addWidget(rightPanel, 1); // 设置比例为1

  // 连接信号和槽
  connect(minButton, &QPushButton::clicked, this, &LoginWindow::showMinimized);
  connect(maxButton, &QPushButton::clicked, this, [this]() {
    if (isMaximized()) {
      showNormal();
    } else {
      showMaximized();
    }
  });
  connect(closeButton, &QPushButton::clicked, this, &LoginWindow::close);
  connect(loginButton, &QPushButton::clicked, this,
          &LoginWindow::onLoginClicked);
  connect(forgotButton, &QPushButton::clicked, this,
          &LoginWindow::onForgotPasswordClicked);

  // 安装事件过滤器 - 使用container作为拖拽区域
  container->installEventFilter(this);

  // 保存图片标签的引用，用于在 resizeEvent 中更新
  m_imageLabel = imageLabel;
  m_bgImage = bgImage;

  // 延迟执行图片缩放，确保布局完成后再缩放
  QTimer::singleShot(0, this, &LoginWindow::updateImageScale);

  // 延迟执行窗口居中，确保窗口完全初始化后再居中
  QTimer::singleShot(100, this, &LoginWindow::centerWindow);

  // 延迟定位控制按钮组
  QTimer::singleShot(50, this, [this]() {
    if (m_controlButtons) {
      m_controlButtons->move(width() - 100 - 20 - 20, 10 + 20);
    }
  });
}

void LoginWindow::loadStyleSheet() {
  QFile file(":/styles/login.qss");
  if (file.open(QFile::ReadOnly | QFile::Text)) {
    QString styleSheet = QLatin1String(file.readAll());
    qApp->setStyleSheet(styleSheet);
    file.close();
  } else {
    qDebug() << "Failed to load style sheet from path: :/styles/login.qss";
  }
}

/**
 * @brief Handles the login button click event.
 *
 * Validates the username and password inputs, constructs a login request,
 * and sends it to the API. On successful login, triggers onLoginSuccess();
 * on failure, triggers onLoginFailed() with the error message.
 *
 * The function performs the following steps:
 * 1. Retrieves username and password from input fields
 * 2. Validates the inputs using FormUtils
 * 3. Prepares JSON data for the login request
 * 4. Sends the request via Api::login()
 * 5. Handles the response asynchronously
 */
void LoginWindow::onLoginClicked() {
  QString username = usernameEdit->text();
  QString password = passwordEdit->text();

  if (!FormUtils::validateRequired(username, "用户名") ||
      !FormUtils::validateRequired(password, "密码")) {
    Logger::getInstance().log(Logger::Warning,
                              "登录表单验证失败：用户名或密码为空");
    return;
  }

  Logger::getInstance().log(Logger::Info,
                            QString("开始登录请求，用户名：%1").arg(username));

  loginButton->setEnabled(false);

  // 构造登录数据
  QJsonObject loginData;
  loginData["username"] = username;
  loginData["password"] = password;

  // 使用 NetworkManager 发送请求，登录接口不需要认证
  NetworkManager::getInstance().post(
      Api::LOGIN, loginData, QMap<QString, QString>(), QMap<QString, QString>(),
      [=](const QJsonObject &response) {
        // 成功回调
        loginButton->setEnabled(true);

        Logger::getInstance().log(
            Logger::Debug, QString("登录响应数据: %1")
                               .arg(QString(QJsonDocument(response).toJson(
                                   QJsonDocument::Compact))));

        // 提取并保存token
        if (response.contains("data") && response["data"].isObject()) {
          QJsonObject data = response["data"].toObject();
          if (data.contains("token")) {
            QString token = data["token"].toString();
            if (!token.isEmpty()) {
              // 使用NetworkManager设置认证token
              NetworkManager::getInstance().setAuthToken(token);
              Logger::getInstance().log(Logger::Info, "Token已保存到存储中");
            }
          }
        }

        // 登录成功，直接调用成功处理
        onLoginSuccess();
      },
      [=](const QString &error) {
        // 错误回调
        loginButton->setEnabled(true);

        onLoginFailed(error);
      },
      false); // 登录接口不需要认证
}

void LoginWindow::onForgotPasswordClicked() {
  // TODO: 实现忘记密码功能
  Toast::info("忘记密码功能开发中...", this);
}

void LoginWindow::onLoginSuccess() {
  // 登录成功处理
  Logger::getInstance().log(Logger::Info, "显示登录成功提示");
  Toast::success("欢迎回来！", this);

  // 创建并显示主窗口
  Logger::getInstance().log(Logger::Debug, "创建主窗口");
  MainWindow *mainWindow = new MainWindow();
  mainWindow->show();

  // 关闭登录窗口
  Logger::getInstance().log(Logger::Debug, "关闭登录窗口");
  this->close();
}

void LoginWindow::onLoginFailed(const QString &error) {
  // 登录失败处理
  Logger::getInstance().log(Logger::Warning,
                            QString("显示登录失败提示：%1").arg(error));
  loginButton->setEnabled(true);
}

void LoginWindow::showMainWindow() {
  // 创建并显示主窗口
  MainWindow *mainWindow = new MainWindow();
  mainWindow->show();

  // 关闭登录窗口
  close();
}

void LoginWindow::paintEvent(QPaintEvent *event) {
  Q_UNUSED(event);
  QPainter painter(this);
  painter.setRenderHint(QPainter::Antialiasing);

  // 创建圆角矩形路径
  QPainterPath path;
  path.addRoundedRect(rect(), 10, 10);

  // 设置背景色和阴影
  QColor shadowColor(0, 0, 0, 30);
  for (int i = 0; i < 10; i++) {
    QPainterPath shadowPath;
    shadowPath.addRoundedRect(rect().adjusted(i, i, -i, -i), 10, 10);
    painter.fillPath(shadowPath, shadowColor);
  }
  painter.fillPath(path, QColor(255, 255, 255));
}

bool LoginWindow::eventFilter(QObject *watched, QEvent *event) {
  if (watched->objectName() == "loginContainer") {
    if (event->type() == QEvent::MouseButtonPress) {
      QMouseEvent *mouseEvent = static_cast<QMouseEvent *>(event);
      if (mouseEvent->button() == Qt::LeftButton) {
        m_dragPosition = mouseEvent->globalPos() - frameGeometry().topLeft();
        event->accept();
        return true;
      }
    } else if (event->type() == QEvent::MouseMove) {
      QMouseEvent *mouseEvent = static_cast<QMouseEvent *>(event);
      if (mouseEvent->buttons() & Qt::LeftButton) {
        move(mouseEvent->globalPos() - m_dragPosition);
        event->accept();
        return true;
      }
    }
  }
  return QWidget::eventFilter(watched, event);
}

void LoginWindow::resizeEvent(QResizeEvent *event) {
  QWidget::resizeEvent(event);
  updateImageScale();

  // 重新定位控制按钮组 (保持 top: 10px, right: 20px 的绝对定位)
  if (m_controlButtons) {
    m_controlButtons->move(width() - 100 - 20 - 20,
                           10 + 20); // 考虑主容器的margin
  }
}

void LoginWindow::updateImageScale() {
  if (!m_bgImage.isNull() && m_imageLabel) {
    // 获取imageLabel的实际大小
    QSize labelSize = m_imageLabel->size();

    // 如果标签大小还没有正确设置，延迟处理
    if (labelSize.width() <= 0 || labelSize.height() <= 0) {
      QTimer::singleShot(0, this, &LoginWindow::updateImageScale);
      return;
    }

    // 确保不超过最大宽度限制
    int maxWidth = qMin(labelSize.width(), 600);
    QSize constrainedSize(maxWidth, labelSize.height());

    // 计算缩放后的尺寸，使图片完全适应容器而不超出
    QSize imageSize = m_bgImage.size();
    QSize scaledSize = imageSize;

    // 使用KeepAspectRatio确保图片完全适应容器，不会超出任何边界
    scaledSize.scale(constrainedSize, Qt::KeepAspectRatio);

    // 缩放图片并设置
    QPixmap scaledPixmap = m_bgImage.scaled(scaledSize, Qt::KeepAspectRatio,
                                            Qt::SmoothTransformation);

    m_imageLabel->setPixmap(scaledPixmap);

    Logger::getInstance().log(
        Logger::Debug,
        QString(
            "图片缩放: 容器大小(%1x%2) -> 约束大小(%3x%4) -> 图片大小(%5x%6)")
            .arg(labelSize.width())
            .arg(labelSize.height())
            .arg(constrainedSize.width())
            .arg(constrainedSize.height())
            .arg(scaledSize.width())
            .arg(scaledSize.height()));
  }
}

void LoginWindow::centerWindow() {
  // 获取主屏幕
  QScreen *screen = QGuiApplication::primaryScreen();
  if (!screen) {
    return;
  }

  // 获取屏幕的可用几何区域（排除任务栏等）
  QRect screenGeometry = screen->availableGeometry();

  // 计算窗口应该放置的位置（居中）
  int x = screenGeometry.x() + (screenGeometry.width() - width()) / 2;
  int y = screenGeometry.y() + (screenGeometry.height() - height()) / 2;

  // 确保窗口不会超出屏幕边界
  x = qMax(screenGeometry.x(),
           qMin(x, screenGeometry.x() + screenGeometry.width() - width()));
  y = qMax(screenGeometry.y(),
           qMin(y, screenGeometry.y() + screenGeometry.height() - height()));

  // 移动窗口到计算出的位置
  move(x, y);

  Logger::getInstance().log(
      Logger::Debug,
      QString("窗口居中: 屏幕大小(%1x%2) 窗口大小(%3x%4) 位置(%5,%6)")
          .arg(screenGeometry.width())
          .arg(screenGeometry.height())
          .arg(width())
          .arg(height())
          .arg(x)
          .arg(y));
}