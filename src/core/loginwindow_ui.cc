#include "core/loginwindow_ui.h"
#include "api/api.h"
#include "core/app_config.h"
#include "core/mainwindow_ui.h"
#include "ui_loginwindow.h"
#include "utils/formutils.h"
#include "utils/logger.h"
#include "utils/storage.h"
#include "utils/toast.h"
#include <QApplication>
#include <QGuiApplication>
#include <QScreen>

#include <QCheckBox>
#include <QDebug>
#include <QFile>
#include <QHBoxLayout>
#include <QJsonDocument>
#include <QJsonObject>
#include <QLabel>
#include <QLineEdit>
#include <QMouseEvent>
#include <QPainter>
#include <QPainterPath>
#include <QPushButton>
#include <QSizePolicy>
#include <QTimer>
#include <QVBoxLayout>

LoginWindowUI::LoginWindowUI(QWidget *parent)
    : QWidget(parent), ui(new Ui::LoginWindow) {
  ui->setupUi(this);
  setupUI();
  connectSignals();
  loadStyleSheet();
}

LoginWindowUI::~LoginWindowUI() { delete ui; }

void LoginWindowUI::setupUI() {
  // 设置窗口属性
  // 智能计算窗口大小：屏幕的一半，但不超过合理的最大尺寸
  QScreen *screen = QGuiApplication::primaryScreen();
  QRect screenGeometry = screen->geometry();

  // 计算屏幕一半的尺寸
  int halfScreenWidth = screenGeometry.width() / 2;
  int halfScreenHeight = screenGeometry.height() / 2;

  // 设置合理的最大尺寸限制（从配置文件读取）
  const int MAX_LOGIN_WIDTH = LOGIN_MAX_WIDTH;   // 最大宽度
  const int MAX_LOGIN_HEIGHT = LOGIN_MAX_HEIGHT; // 最大高度
  const int MIN_LOGIN_WIDTH = LOGIN_MIN_WIDTH;   // 最小宽度
  const int MIN_LOGIN_HEIGHT = LOGIN_MIN_HEIGHT; // 最小高度

  // 智能选择窗口大小
  int windowWidth =
      qMax(MIN_LOGIN_WIDTH, qMin(halfScreenWidth, MAX_LOGIN_WIDTH));
  int windowHeight =
      qMax(MIN_LOGIN_HEIGHT, qMin(halfScreenHeight, MAX_LOGIN_HEIGHT));

  resize(windowWidth, windowHeight);

  Logger::getInstance().log(
      Logger::Debug,
      QString("智能窗口大小: 屏幕(%1x%2) -> 一半(%3x%4) -> 最终(%5x%6)")
          .arg(screenGeometry.width())
          .arg(screenGeometry.height())
          .arg(halfScreenWidth)
          .arg(halfScreenHeight)
          .arg(windowWidth)
          .arg(windowHeight));

  // 窗口居中显示
  centerWindow();

  setWindowFlags(Qt::FramelessWindowHint);
  setAttribute(Qt::WA_TranslucentBackground);
  setAttribute(Qt::WA_NoSystemBackground);

  // 设置背景图片
  QPixmap bgImage(":/images/login-bg.png");
  if (!bgImage.isNull()) {
    Logger::getInstance().log(Logger::Debug,
                              "Successfully loaded login background image");
    m_bgImage = bgImage;
    ui->imageLabel->setPixmap(bgImage);
    ui->imageLabel->setStyleSheet(
        "border-top-left-radius: 8px; border-bottom-left-radius: 8px;");
  } else {
    Logger::getInstance().log(Logger::Warning,
                              "Failed to load login background image from "
                              "path: :/images/login-bg.png");
  }

  // 设置标题文字（从配置文件读取）
  ui->titleLabel->setText(LOGIN_TITLE);

  // 设置控制按钮组为绝对定位 (类似前端 position: absolute)
  setupAbsoluteControlButtons();

  // 安装事件过滤器 - 使用主窗口widget代替titleBar
  this->installEventFilter(this);

  // 延迟执行图片缩放，确保布局完成后再缩放
  QTimer::singleShot(0, this, &LoginWindowUI::updateImageScale);

  // 延迟执行窗口居中，确保窗口完全初始化后再居中
  QTimer::singleShot(100, this, &LoginWindowUI::centerWindow);
}

void LoginWindowUI::connectSignals() {
  // 连接窗口控制按钮
  connect(ui->minButton, &QPushButton::clicked, this,
          &LoginWindowUI::showMinimized);
  connect(ui->maxButton, &QPushButton::clicked, this, [this]() {
    if (isMaximized()) {
      showNormal();
    } else {
      showMaximized();
    }
  });
  connect(ui->closeButton, &QPushButton::clicked, this, &LoginWindowUI::close);

  // 连接登录相关按钮
  connect(ui->loginButton, &QPushButton::clicked, this,
          &LoginWindowUI::onLoginClicked);
  connect(ui->forgotButton, &QPushButton::clicked, this,
          &LoginWindowUI::onForgotPasswordClicked);
}

void LoginWindowUI::loadStyleSheet() {
  QFile file(":/styles/login.qss");
  if (file.open(QFile::ReadOnly | QFile::Text)) {
    QString styleSheet = QLatin1String(file.readAll());
    qApp->setStyleSheet(styleSheet);
    file.close();
  } else {
    qDebug() << "Failed to load style sheet from path: :/styles/login.qss";
  }
}

void LoginWindowUI::onLoginClicked() {
  QString username = ui->usernameEdit->text();
  QString password = ui->passwordEdit->text();

  if (!FormUtils::validateRequired(username, "用户名") ||
      !FormUtils::validateRequired(password, "密码")) {
    Logger::getInstance().log(Logger::Warning,
                              "登录表单验证失败：用户名或密码为空");
    return;
  }

  Logger::getInstance().log(Logger::Info,
                            QString("开始登录请求，用户名：%1").arg(username));

  ui->loginButton->setEnabled(false);

  // 临时测试：如果用户名是test，直接跳转到主窗口
  if (username == "test" && password == "test") {
    Logger::getInstance().log(Logger::Info, "使用测试账号，直接跳转到主窗口");
    ui->loginButton->setEnabled(true);
    onLoginSuccess();
    return;
  }

  // 构造登录数据
  QJsonObject loginData;
  loginData["username"] = username;
  loginData["password"] = password;

  // 使用 NetworkManager 发送请求，登录接口不需要认证
  NetworkManager::getInstance().post(
      Api::LOGIN, loginData, QMap<QString, QString>(), QMap<QString, QString>(),
      [=](const QJsonObject &response) {
        // 成功回调
        ui->loginButton->setEnabled(true);

        Logger::getInstance().log(
            Logger::Debug, QString("登录响应数据: %1")
                               .arg(QString(QJsonDocument(response).toJson(
                                   QJsonDocument::Compact))));

        // 提取并保存token
        if (response.contains("data") && response["data"].isObject()) {
          QJsonObject data = response["data"].toObject();
          if (data.contains("token")) {
            QString token = data["token"].toString();
            if (!token.isEmpty()) {
              // 使用NetworkManager设置认证token
              NetworkManager::getInstance().setAuthToken(token);
              Logger::getInstance().log(Logger::Info, "Token已保存到存储中");
            }
          }
        }

        // 登录成功，直接调用成功处理
        onLoginSuccess();
      },
      [=](const QString &error) {
        // 错误回调
        ui->loginButton->setEnabled(true);
        onLoginFailed(error);
      },
      false); // 登录接口不需要认证
}

void LoginWindowUI::onForgotPasswordClicked() {
  // TODO: 实现忘记密码功能
  Toast::info("忘记密码功能开发中...", this);
}

void LoginWindowUI::onLoginSuccess() {
  // 登录成功处理
  Logger::getInstance().log(Logger::Info, "显示登录成功提示");
  Toast::success("欢迎回来！", this);

  // 创建并显示主窗口
  Logger::getInstance().log(Logger::Debug, "创建主窗口");
  MainWindowUI *mainWindow = new MainWindowUI();
  mainWindow->show();

  // 关闭登录窗口
  Logger::getInstance().log(Logger::Debug, "关闭登录窗口");
  this->close();
}

void LoginWindowUI::onLoginFailed(const QString &error) {
  // 登录失败处理
  Logger::getInstance().log(Logger::Warning,
                            QString("显示登录失败提示：%1").arg(error));
  ui->loginButton->setEnabled(true);
}

void LoginWindowUI::showMainWindow() {
  // 创建并显示主窗口
  MainWindowUI *mainWindow = new MainWindowUI();
  mainWindow->show();

  // 关闭登录窗口
  close();
}

void LoginWindowUI::paintEvent(QPaintEvent *event) {
  Q_UNUSED(event);
  QPainter painter(this);
  painter.setRenderHint(QPainter::Antialiasing);

  // 创建圆角矩形路径
  QPainterPath path;
  path.addRoundedRect(rect(), 10, 10);

  // 设置背景色和阴影
  QColor shadowColor(0, 0, 0, 30);
  for (int i = 0; i < 10; i++) {
    QPainterPath shadowPath;
    shadowPath.addRoundedRect(rect().adjusted(i, i, -i, -i), 10, 10);
    painter.fillPath(shadowPath, shadowColor);
  }
  painter.fillPath(path, QColor(255, 255, 255));
}

bool LoginWindowUI::eventFilter(QObject *watched, QEvent *event) {
  if (watched == this) {
    if (event->type() == QEvent::MouseButtonPress) {
      QMouseEvent *mouseEvent = static_cast<QMouseEvent *>(event);
      if (mouseEvent->button() == Qt::LeftButton) {
        m_dragPosition = mouseEvent->globalPos() - frameGeometry().topLeft();
        event->accept();
      }
    } else if (event->type() == QEvent::MouseMove) {
      QMouseEvent *mouseEvent = static_cast<QMouseEvent *>(event);
      if (mouseEvent->buttons() & Qt::LeftButton) {
        move(mouseEvent->globalPos() - m_dragPosition);
        event->accept();
      }
    }
  }
  return QWidget::eventFilter(watched, event);
}

void LoginWindowUI::resizeEvent(QResizeEvent *event) {
  QWidget::resizeEvent(event);
  updateImageScale();

  // 重新定位控制按钮组 (保持 top: 10px, right: 20px 的绝对定位)
  if (m_controlButtonsContainer) {
    m_controlButtonsContainer->move(width() - 100 - 20, 10);
  }
}

void LoginWindowUI::updateImageScale() {
  if (!m_bgImage.isNull() && ui->imageLabel) {
    // 获取imageLabel的实际大小
    QSize labelSize = ui->imageLabel->size();

    // 如果标签大小还没有正确设置，延迟处理
    if (labelSize.width() <= 0 || labelSize.height() <= 0) {
      QTimer::singleShot(0, this, &LoginWindowUI::updateImageScale);
      return;
    }

    // 确保不超过最大宽度限制
    int maxWidth = qMin(labelSize.width(), 600);
    QSize constrainedSize(maxWidth, labelSize.height());

    // 计算缩放后的尺寸，使图片完全适应容器而不超出
    QSize imageSize = m_bgImage.size();
    QSize scaledSize = imageSize;

    // 使用KeepAspectRatio确保图片完全适应容器，不会超出任何边界
    scaledSize.scale(constrainedSize, Qt::KeepAspectRatio);

    // 缩放图片并设置
    QPixmap scaledPixmap = m_bgImage.scaled(scaledSize, Qt::KeepAspectRatio,
                                            Qt::SmoothTransformation);

    ui->imageLabel->setPixmap(scaledPixmap);

    Logger::getInstance().log(
        Logger::Debug,
        QString(
            "图片缩放: 容器大小(%1x%2) -> 约束大小(%3x%4) -> 图片大小(%5x%6)")
            .arg(labelSize.width())
            .arg(labelSize.height())
            .arg(constrainedSize.width())
            .arg(constrainedSize.height())
            .arg(scaledSize.width())
            .arg(scaledSize.height()));
  }
}

void LoginWindowUI::centerWindow() {
  // 获取主屏幕
  QScreen *screen = QGuiApplication::primaryScreen();
  if (!screen) {
    return;
  }

  // 获取屏幕的可用几何区域（排除任务栏等）
  QRect screenGeometry = screen->availableGeometry();

  // 计算窗口应该放置的位置（居中）
  int x = screenGeometry.x() + (screenGeometry.width() - width()) / 2;
  int y = screenGeometry.y() + (screenGeometry.height() - height()) / 2;

  // 确保窗口不会超出屏幕边界
  x = qMax(screenGeometry.x(),
           qMin(x, screenGeometry.x() + screenGeometry.width() - width()));
  y = qMax(screenGeometry.y(),
           qMin(y, screenGeometry.y() + screenGeometry.height() - height()));

  // 移动窗口到计算出的位置
  move(x, y);

  Logger::getInstance().log(
      Logger::Debug,
      QString("窗口居中: 屏幕大小(%1x%2) 窗口大小(%3x%4) 位置(%5,%6)")
          .arg(screenGeometry.width())
          .arg(screenGeometry.height())
          .arg(width())
          .arg(height())
          .arg(x)
          .arg(y));
}

void LoginWindowUI::setupAbsoluteControlButtons() {
  // 创建一个新的容器来包含控制按钮，实现绝对定位
  m_controlButtonsContainer = new QWidget(this);
  m_controlButtonsContainer->setFixedSize(100, 40);
  m_controlButtonsContainer->setObjectName("absoluteControlButtons");

  // 设置绝对定位：top: 10px, right: 20px
  // 初始位置会在resizeEvent中正确设置
  m_controlButtonsContainer->move(0, 0);

  // 确保按钮组在最上层显示
  m_controlButtonsContainer->raise();

  // 创建布局
  QHBoxLayout *layout = new QHBoxLayout(m_controlButtonsContainer);
  layout->setContentsMargins(0, 0, 0, 0);
  layout->setSpacing(8);

  // 将原有的按钮移动到新容器中
  ui->closeButton->setParent(m_controlButtonsContainer);
  ui->minButton->setParent(m_controlButtonsContainer);
  ui->maxButton->setParent(m_controlButtonsContainer);

  layout->addWidget(ui->closeButton);
  layout->addWidget(ui->minButton);
  layout->addWidget(ui->maxButton);

  // 重新连接控制按钮的信号（因为setParent可能会断开连接）
  connect(ui->minButton, &QPushButton::clicked, this,
          &LoginWindowUI::showMinimized);
  connect(ui->maxButton, &QPushButton::clicked, this, [this]() {
    if (isMaximized()) {
      showNormal();
    } else {
      showMaximized();
    }
  });
  connect(ui->closeButton, &QPushButton::clicked, this, &LoginWindowUI::close);

  // 延迟定位控制按钮组
  QTimer::singleShot(50, this, [this]() {
    if (m_controlButtonsContainer) {
      m_controlButtonsContainer->move(width() - 100 - 20, 10);
    }
  });
}
