#include "core/loginwindow_ui.h"
#include "utils/logger.h"
#include <QApplication>

int main(int argc, char *argv[]) {
  QApplication app(argc, argv);

  // 设置应用程序信息
  app.setApplicationName("Aiwuu在线课堂");
  app.setApplicationVersion("1.0.0");
  app.setOrganizationName("Aiwuu");
  app.setOrganizationDomain("aiwuu.com");

  // 初始化日志系统
  Logger::getInstance().init();
  Logger::getInstance().log(Logger::Info, "应用程序启动");

  // 创建并显示登录窗口
  LoginWindowUI loginWindow;
  loginWindow.show();

  Logger::getInstance().log(Logger::Info, "登录窗口已显示");

  return app.exec();
}