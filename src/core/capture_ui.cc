#include "core/capture_ui.h"
#include "ui_capture.h"
#include "utils/logger.h"
#include "utils/toast.h"

#include <QDateTime>

CaptureUI::CaptureUI(QWidget *parent)
    : QWidget(parent), ui(new Ui::Capture), isCollecting(false),
      progressValue(0) {
  ui->setupUi(this);
  setupUI();
  setupConnections();

  progressTimer = new QTimer(this);
  connect(progressTimer, &QTimer::timeout, this, &CaptureUI::updateProgress);

  Logger::getInstance().log(Logger::Info, "CaptureUI页面初始化完成");
}

CaptureUI::~CaptureUI() {
  if (progressTimer) {
    progressTimer->stop();
  }
  delete ui;
  Logger::getInstance().log(Logger::Info, "FxmyUI页面销毁");
}

void CaptureUI::setupUI() {
  setWindowTitle("数据采集 - Fxmy");
  resize(800, 600);

  // 设置初始日志
  ui->logTextEdit->append(
      QString("[%1] 系统初始化完成")
          .arg(QDateTime::currentDateTime().toString("hh:mm:ss")));

  Logger::getInstance().log(Logger::Info, "FxmyUI界面设置完成");
}

void CaptureUI::setupConnections() {
  connect(ui->backButton, &QPushButton::clicked, this,
          &CaptureUI::onBackClicked);
  connect(ui->startButton, &QPushButton::clicked, this,
          &CaptureUI::onStartCollectionClicked);
  connect(ui->stopButton, &QPushButton::clicked, this,
          &CaptureUI::onStopCollectionClicked);
}

void CaptureUI::onStartCollectionClicked() {
  Logger::getInstance().log(Logger::Info, "用户点击开始采集");
  startCollection();
}

void CaptureUI::onStopCollectionClicked() {
  Logger::getInstance().log(Logger::Info, "用户点击停止采集");
  stopCollection();
}

void CaptureUI::onBackClicked() {
  Logger::getInstance().log(Logger::Info, "用户点击返回主窗口");
  emit backToMainWindow();
}

void CaptureUI::startCollection() {
  isCollecting = true;
  progressValue = 0;

  ui->startButton->setEnabled(false);
  ui->stopButton->setEnabled(true);
  ui->statusLabel->setText("采集中...");
  ui->statusLabel->setStyleSheet(
      "font-size: 16px; color: #ffc107; font-weight: bold;");

  ui->progressBar->setValue(0);
  ui->logTextEdit->append(
      QString("[%1] 开始数据采集...")
          .arg(QDateTime::currentDateTime().toString("hh:mm:ss")));

  // 启动进度更新定时器
  progressTimer->start(100); // 每100ms更新一次

  Toast::info("数据采集已开始", this);
}

void CaptureUI::stopCollection() {
  isCollecting = false;

  progressTimer->stop();

  ui->startButton->setEnabled(true);
  ui->stopButton->setEnabled(false);
  ui->statusLabel->setText("已停止");
  ui->statusLabel->setStyleSheet(
      "font-size: 16px; color: #dc3545; font-weight: bold;");

  ui->logTextEdit->append(
      QString("[%1] 数据采集已停止")
          .arg(QDateTime::currentDateTime().toString("hh:mm:ss")));

  Toast::info("数据采集已停止", this);
}

void CaptureUI::updateProgress() {
  if (!isCollecting)
    return;

  progressValue += 1;
  ui->progressBar->setValue(progressValue);

  // 模拟采集日志
  if (progressValue % 10 == 0) {
    ui->logTextEdit->append(
        QString("[%1] 采集进度: %2%")
            .arg(QDateTime::currentDateTime().toString("hh:mm:ss"))
            .arg(progressValue));
  }

  // 采集完成
  if (progressValue >= 100) {
    progressTimer->stop();
    isCollecting = false;

    ui->startButton->setEnabled(true);
    ui->stopButton->setEnabled(false);
    ui->statusLabel->setText("采集完成");
    ui->statusLabel->setStyleSheet(
        "font-size: 16px; color: #28a745; font-weight: bold;");

    ui->logTextEdit->append(
        QString("[%1] 数据采集完成！")
            .arg(QDateTime::currentDateTime().toString("hh:mm:ss")));

    Toast::success("数据采集完成！", this);
  }
}