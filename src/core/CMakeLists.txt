add_library(core
    mainwindow.cc
    loginwindow.cc
    capture.cc
)

target_include_directories(core
    PUBLIC
        ${CMAKE_SOURCE_DIR}/include
        ${CMAKE_SOURCE_DIR}/include/core
        ${CMAKE_SOURCE_DIR}/include/utils
        ${CMAKE_SOURCE_DIR}/include/api
    PRIVATE
        ${CMAKE_CURRENT_SOURCE_DIR}
)

# 添加fxmy库的目录
find_library(FXMY_LIBRARY fxmy PATHS ${CMAKE_SOURCE_DIR}/lib)

target_link_libraries(core
    PUBLIC
        Qt5::Core
        Qt5::Gui
        Qt5::Widgets
    PRIVATE
        utils
        api
        ${FXMY_LIBRARY} # 链接fxmy库
)