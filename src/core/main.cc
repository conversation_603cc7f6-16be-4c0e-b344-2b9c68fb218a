#include "core/loginwindow.h"
#include "core/mainwindow.h"
#include "utils/logger.h"
#include "utils/networkmanager.h"
#include "utils/storage.h"
#include <QApplication>
#include <QIcon>

int main(int argc, char *argv[]) {
  QApplication app(argc, argv);

  // 初始化日志系统
  Logger::Config logConfig;
  logConfig.maxDays = 30;                                           // 保留30天
  logConfig.maxFileSize = 10 * 1024 * 1024;                         // 10MB
  logConfig.maxBackupFiles = 10;                                    // 10个备份
  logConfig.pattern = "%d{yyyy-MM-dd HH:mm:ss.zzz} [%p] %c - %m%n"; // 日志格式
  logConfig.level = Logger::Debug; // 开发环境使用 Debug 级别
  Logger::getInstance().init(QString(), logConfig);

  // 设置环境
  NetworkManager::getInstance().setEnvironment("dev");

  // 设置应用程序图标
  app.setWindowIcon(QIcon(":/icons/app.png"));

  // 设置应用程序信息
  QApplication::setApplicationName("Aiwuu Classroom");
  QApplication::setApplicationVersion("1.0.0");
  QApplication::setOrganizationName("Aiwuu");
  QApplication::setOrganizationDomain("aiwuu.com");

  // 检查token是否存在
  QWidget *window = nullptr;
  if (Storage::getInstance().hasItem("token")) {
    Logger::getInstance().log(Logger::Info, "Token存在，直接进入主界面");
    window = new MainWindow();
  } else {
    Logger::getInstance().log(Logger::Info, "Token不存在，进入登录界面");
    window = new LoginWindow();
  }
  window->show();

  return app.exec();
}