#include "utils/confirmdialog.h"
#include <QScreen>
#include <QApplication>
#include <QPainter>
#include <QPainterPath>
#include <QCloseEvent>

ConfirmDialog::ConfirmDialog(QWidget *parent)
    : QDialog(parent), m_opacity(0.0)
{
    setupUI();
    setupStyle();
    setupAnimation();
}

ConfirmDialog::~ConfirmDialog()
{
}

void ConfirmDialog::setupUI()
{
    // 设置窗口属性
    setWindowFlags(Qt::Dialog | Qt::FramelessWindowHint);
    setAttribute(Qt::WA_TranslucentBackground);
    setFixedSize(400, 240);

    // 创建主布局
    mainLayout = new QVBoxLayout(this);
    mainLayout->setContentsMargins(20, 20, 20, 20);
    mainLayout->setSpacing(15);

    // 创建图标
    iconLabel = new QLabel(this);
    iconLabel->setFixedSize(48, 48);
    iconLabel->setAlignment(Qt::AlignCenter);
    mainLayout->addWidget(iconLabel, 0, Qt::AlignCenter);

    // 创建标题
    titleLabel = new QLabel(this);
    titleLabel->setStyleSheet("font-size: 18px; font-weight: bold; color: #333;");
    titleLabel->setAlignment(Qt::AlignCenter);
    mainLayout->addWidget(titleLabel);

    // 创建消息
    messageLabel = new QLabel(this);
    messageLabel->setStyleSheet("font-size: 14px; color: #666;");
    messageLabel->setWordWrap(true);
    messageLabel->setAlignment(Qt::AlignCenter);
    mainLayout->addWidget(messageLabel);

    // 创建按钮布局
    buttonLayout = new QHBoxLayout();
    buttonLayout->setSpacing(10);

    // 创建取消按钮
    cancelButton = new QPushButton("取消", this);
    cancelButton->setFixedSize(100, 36);
    cancelButton->setObjectName("cancelButton");
    buttonLayout->addWidget(cancelButton);

    // 创建确认按钮
    confirmButton = new QPushButton("确认", this);
    confirmButton->setFixedSize(100, 36);
    confirmButton->setObjectName("confirmButton");
    buttonLayout->addWidget(confirmButton);

    mainLayout->addLayout(buttonLayout);

    // 连接信号槽
    connect(confirmButton, &QPushButton::clicked, this, &QDialog::accept);
    connect(cancelButton, &QPushButton::clicked, this, &QDialog::reject);
}

void ConfirmDialog::setupStyle()
{
    // 设置窗口样式
    setStyleSheet(R"(
        QDialog {
            background-color: white;
            border-radius: 10px;
        }
        QPushButton {
            border-radius: 4px;
            padding: 8px 16px;
            font-size: 14px;
        }
        QPushButton#confirmButton {
            background-color: #1890ff;
            color: white;
            border: none;
        }
        QPushButton#confirmButton:hover {
            background-color: #40a9ff;
        }
        QPushButton#confirmButton:pressed {
            background-color: #096dd9;
        }
        QPushButton#cancelButton {
            background-color: #f5f5f5;
            color: #666;
            border: 1px solid #d9d9d9;
        }
        QPushButton#cancelButton:hover {
            background-color: #e6e6e6;
        }
        QPushButton#cancelButton:pressed {
            background-color: #d9d9d9;
        }
    )");

    // 添加阴影效果
    QGraphicsDropShadowEffect *shadow = new QGraphicsDropShadowEffect(this);
    shadow->setBlurRadius(20);
    shadow->setColor(QColor(0, 0, 0, 50));
    shadow->setOffset(0, 0);
    setGraphicsEffect(shadow);
}

void ConfirmDialog::setupAnimation()
{
    // 设置透明度效果
    opacityEffect = new QGraphicsOpacityEffect(this);
    opacityEffect->setOpacity(0.0);
    setGraphicsEffect(opacityEffect);

    // 创建显示动画
    showAnimation = new QPropertyAnimation(opacityEffect, "opacity", this);
    showAnimation->setDuration(200);
    showAnimation->setStartValue(0.0);
    showAnimation->setEndValue(1.0);
    showAnimation->setEasingCurve(QEasingCurve::OutCubic);

    // 创建隐藏动画
    hideAnimation = new QPropertyAnimation(opacityEffect, "opacity", this);
    hideAnimation->setDuration(200);
    hideAnimation->setStartValue(1.0);
    hideAnimation->setEndValue(0.0);
    hideAnimation->setEasingCurve(QEasingCurve::InCubic);

    connect(hideAnimation, &QPropertyAnimation::finished, this, &QDialog::reject);
}

void ConfirmDialog::showEvent(QShowEvent *event)
{
    QDialog::showEvent(event);
    showAnimation->start();
}

void ConfirmDialog::closeEvent(QCloseEvent *event)
{
    hideAnimation->start();
    event->ignore();
}

void ConfirmDialog::setTitle(const QString &title)
{
    titleLabel->setText(title);
}

void ConfirmDialog::setMessage(const QString &message)
{
    messageLabel->setText(message);
}

void ConfirmDialog::setConfirmText(const QString &text)
{
    confirmButton->setText(text);
}

void ConfirmDialog::setCancelText(const QString &text)
{
    cancelButton->setText(text);
}

void ConfirmDialog::setIcon(const QString &iconPath)
{
    if (!iconPath.isEmpty())
    {
        QPixmap pixmap(iconPath);
        iconLabel->setPixmap(pixmap.scaled(48, 48, Qt::KeepAspectRatio, Qt::SmoothTransformation));
        iconLabel->show();
    }
    else
    {
        iconLabel->hide();
    }
}

qreal ConfirmDialog::opacity() const
{
    return m_opacity;
}

void ConfirmDialog::setOpacity(qreal opacity)
{
    m_opacity = opacity;
    update();
}

bool ConfirmDialog::confirm(QWidget *parent,
                            const QString &title,
                            const QString &message,
                            const QString &confirmText,
                            const QString &cancelText,
                            const QString &iconPath)
{
    ConfirmDialog dialog(parent);
    dialog.setTitle(title);
    dialog.setMessage(message);
    dialog.setConfirmText(confirmText);
    dialog.setCancelText(cancelText);
    dialog.setIcon(iconPath);

    // 居中显示
    QScreen *screen = QGuiApplication::primaryScreen();
    QRect screenGeometry = screen->geometry();
    int x = (screenGeometry.width() - dialog.width()) / 2;
    int y = (screenGeometry.height() - dialog.height()) / 2;
    dialog.move(x, y);

    return dialog.exec() == QDialog::Accepted;
}