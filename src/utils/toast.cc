#include "utils/toast.h"
#include "utils/logger.h"

#include <QApplication>
#include <QGraphicsOpacityEffect>
#include <QPropertyAnimation>
#include <QScreen>
#include <QTimer>
#include <QVBoxLayout>

Toast::Toast(QWidget *parent) : QWidget(parent), m_opacity(1.0) {
  // 设置窗口属性
  setWindowFlags(Qt::FramelessWindowHint | Qt::Tool | Qt::WindowStaysOnTopHint);
  setAttribute(Qt::WA_TranslucentBackground);
  setAttribute(Qt::WA_ShowWithoutActivating);
  setAttribute(Qt::WA_DeleteOnClose); // 关闭时自动删除

  // 创建标签
  messageLabel = new QLabel(this);
  messageLabel->setAlignment(Qt::AlignCenter);
  messageLabel->setStyleSheet(
      "padding: 10px 20px; border-radius: 4px; color: white;");

  // 创建布局
  QVBoxLayout *layout = new QVBoxLayout(this);
  layout->setContentsMargins(0, 0, 0, 0);
  layout->addWidget(messageLabel);

  // 创建动画效果
  opacityEffect = new QGraphicsOpacityEffect(this);
  setGraphicsEffect(opacityEffect);
  opacityEffect->setOpacity(1.0);

  animation = new QPropertyAnimation(opacityEffect, "opacity", this);
  animation->setDuration(300);

  // 创建定时器
  timer = new QTimer(this);
  timer->setSingleShot(true);
  connect(timer, &QTimer::timeout, this, [this]() {
    animation->setStartValue(1.0);
    animation->setEndValue(0.0);
    animation->start();
    connect(animation, &QPropertyAnimation::finished, this, &QWidget::close);
  });
}

Toast::~Toast() {}

void Toast::success(const QString &message, QWidget *parent, int duration) {
  // 如果没有指定父窗口，自动获取当前活动窗口
  if (!parent) {
    parent = QApplication::activeWindow();
  }
  Toast *toast = new Toast(parent);
  toast->showMessage(Success, message, duration);
}

void Toast::error(const QString &message, QWidget *parent, int duration) {
  // 如果没有指定父窗口，自动获取当前活动窗口
  if (!parent) {
    parent = QApplication::activeWindow();
  }
  Toast *toast = new Toast(parent);
  toast->showMessage(Error, message, duration);
}

void Toast::warning(const QString &message, QWidget *parent, int duration) {
  // 如果没有指定父窗口，自动获取当前活动窗口
  if (!parent) {
    parent = QApplication::activeWindow();
  }
  Toast *toast = new Toast(parent);
  toast->showMessage(Warning, message, duration);
}

void Toast::info(const QString &message, QWidget *parent, int duration) {
  // 如果没有指定父窗口，自动获取当前活动窗口
  if (!parent) {
    parent = QApplication::activeWindow();
  }
  Toast *toast = new Toast(parent);
  toast->showMessage(Info, message, duration);
}

void Toast::showMessage(Type type, const QString &message, int duration) {
  // 调试输出
  Logger::getInstance().log(
      Logger::Debug,
      QString("Toast::showMessage called with message: %1").arg(message));

  // 设置消息样式
  QString style;
  switch (type) {
  case Success:
    style = "background-color: #52c41a;";
    break;
  case Error:
    style = "background-color: #f5222d;";
    break;
  case Warning:
    style = "background-color: #faad14;";
    break;
  case Info:
    style = "background-color: #1890ff;";
    break;
  }
  messageLabel->setStyleSheet(
      style + " padding: 10px 20px; border-radius: 4px; color: white;");
  messageLabel->setText(message);

  // 调整大小
  adjustSize();

  // 确保最小尺寸
  setMinimumSize(200, 50);

  // 计算位置（显示在右上角）
  int x, y;
  if (parentWidget()) {
    // 如果有父窗口，显示在父窗口右上角
    QRect parentGeometry = parentWidget()->geometry();
    x = parentGeometry.x() + parentGeometry.width() - width() -
        20;                      // 距离右边20像素
    y = parentGeometry.y() + 20; // 距离顶部20像素
    move(x, y);
  } else {
    // 如果没有父窗口，显示在屏幕右上角
    QScreen *screen = QApplication::primaryScreen();
    QRect screenGeometry = screen->geometry();
    x = screenGeometry.width() - width() - 20; // 距离右边20像素
    y = 20;                                    // 距离顶部20像素
    move(x, y);
  }

  // 调试输出位置信息
  Logger::getInstance().log(Logger::Debug,
                            QString("Toast position: x=%1, y=%2, size=%3x%4")
                                .arg(x)
                                .arg(y)
                                .arg(width())
                                .arg(height()));

  // 显示窗口
  show();
  raise();          // 确保在最前面
  activateWindow(); // 激活窗口

  // 显示动画
  opacityEffect->setOpacity(0.0);
  animation->setStartValue(0.0);
  animation->setEndValue(1.0);
  animation->start();

  // 设置定时器
  timer->start(duration);

  Logger::getInstance().log(Logger::Debug, "Toast shown successfully");
}

qreal Toast::opacity() const { return m_opacity; }

void Toast::setOpacity(qreal opacity) {
  m_opacity = opacity;
  opacityEffect->setOpacity(opacity);
}