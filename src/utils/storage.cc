#include "utils/storage.h"

#include <QCoreApplication>
#include <QDebug>
#include <QJsonDocument>
#include <QJsonObject>
#include <QFile>
#include <QDir>
#include <QStandardPaths>

const QString Storage::STORAGE_PREFIX = "storage_";

Storage& Storage::getInstance()
{
    static Storage instance;
    return instance;
}

Storage::Storage(QObject* parent)
    : QObject(parent)
{
    // 初始化 QSettings，使用应用程序名称作为组织名
    settings = new QSettings(QCoreApplication::organizationName(),
                           QCoreApplication::applicationName(),
                           this);
}

Storage::~Storage()
{
    delete settings;
}

void Storage::setItem(const QString& key, const QVariant& value)
{
    QString fullKey = STORAGE_PREFIX + key;
    settings->setValue(fullKey, value);
    settings->sync();
    emit storageChanged(key, value);
}

QVariant Storage::getItem(const QString& key, const QVariant& defaultValue) const
{
    QString fullKey = STORAGE_PREFIX + key;
    return settings->value(fullKey, defaultValue);
}

void Storage::removeItem(const QString& key)
{
    QString fullKey = STORAGE_PREFIX + key;
    settings->remove(fullKey);
    settings->sync();
    emit storageRemoved(key);
}

void Storage::clear()
{
    settings->beginGroup(STORAGE_PREFIX);
    settings->remove("");
    settings->endGroup();
    settings->sync();
    emit storageCleared();
}

QStringList Storage::keys() const
{
    QStringList result;
    settings->beginGroup(STORAGE_PREFIX);
    QStringList allKeys = settings->allKeys();
    settings->endGroup();
    
    for (const QString& key : allKeys) {
        result.append(key.mid(STORAGE_PREFIX.length()));
    }
    
    return result;
}

bool Storage::hasItem(const QString& key) const
{
    QString fullKey = STORAGE_PREFIX + key;
    return settings->contains(fullKey);
}

int Storage::size() const
{
    return keys().size();
}

void Storage::setJson(const QString& key, const QJsonObject& json)
{
    QJsonDocument doc(json);
    setItem(key, doc.toJson());
}

QJsonObject Storage::getJson(const QString& key) const
{
    QByteArray data = getItem(key).toByteArray();
    QJsonDocument doc = QJsonDocument::fromJson(data);
    return doc.object();
}

void Storage::setJsonArray(const QString& key, const QJsonArray& array)
{
    QJsonDocument doc(array);
    setItem(key, doc.toJson());
}

QJsonArray Storage::getJsonArray(const QString& key) const
{
    QByteArray data = getItem(key).toByteArray();
    QJsonDocument doc = QJsonDocument::fromJson(data);
    return doc.array();
} 