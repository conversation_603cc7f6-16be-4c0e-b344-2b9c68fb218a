#include "utils/networkmanager.h"
#include "api/api.h"
#include "core/app_config.h"
#include "utils/logger.h"
#include "utils/storage.h"
#include "utils/toast.h"

#include <QDebug>
#include <QJsonDocument>
#include <QJsonObject>
#include <QNetworkReply>
#include <QNetworkRequest>
#include <QUrl>
#include <QUrlQuery>

NetworkManager *NetworkManager::instance = nullptr;

NetworkManager &NetworkManager::getInstance() {
  if (!instance) {
    instance = new NetworkManager();
  }
  return *instance;
}

NetworkManager::NetworkManager(QObject *parent)
    : QObject(parent), manager(new QNetworkAccessManager(this)), maxRetries(3) {
  // 设置默认的 API 前缀
  apiPrefix = "/dev-api";

  // 初始化时根据环境设置 API 地址
  updateApiBaseUrl();

  // 等待网络管理器初始化完成
  connect(manager, &QNetworkAccessManager::finished, this,
          [this](QNetworkReply *reply) {
            if (reply->error() == QNetworkReply::NoError) {
              Logger::getInstance().log(Logger::Debug, "网络管理器初始化完成");
            }
          });
}

NetworkManager::~NetworkManager() { delete manager; }

void NetworkManager::setEnvironment(const QString &env) {
  if (env == "dev") {
    currentEnv = "dev";
  } else if (env == "prod") {
    currentEnv = "prod";
  } else {
    Logger::getInstance().log(
        Logger::Warning,
        QString("未知的环境类型：%1，使用默认环境（dev）").arg(env));
    currentEnv = "dev";
  }

  // 更新 API 地址
  updateApiBaseUrl();
  Logger::getInstance().log(
      Logger::Info,
      QString("切换到%1环境，API地址：%2").arg(currentEnv).arg(apiBaseUrl));
}

void NetworkManager::updateApiBaseUrl() { apiBaseUrl = QString(API_BASE_URL); }

QString NetworkManager::getBaseUrl() const { return apiBaseUrl; }

void NetworkManager::setHeader(const QString &key, const QString &value) {
  headers[key] = value;
}

void NetworkManager::clearHeaders() { headers.clear(); }

void NetworkManager::setMaxRetries(int retries) { maxRetries = retries; }

void NetworkManager::addRequestInterceptor(RequestInterceptor *interceptor) {
  if (interceptor && !requestInterceptors.contains(interceptor)) {
    requestInterceptors.append(interceptor);
  }
}

void NetworkManager::addResponseInterceptor(ResponseInterceptor *interceptor) {
  if (interceptor && !responseInterceptors.contains(interceptor)) {
    responseInterceptors.append(interceptor);
  }
}

void NetworkManager::removeRequestInterceptor(RequestInterceptor *interceptor) {
  requestInterceptors.removeOne(interceptor);
}

void NetworkManager::removeResponseInterceptor(
    ResponseInterceptor *interceptor) {
  responseInterceptors.removeOne(interceptor);
}

QNetworkRequest NetworkManager::createRequest(
    const QString &endpoint, const QMap<QString, QString> &queryParams,
    const QMap<QString, QString> &pathParams, bool requireAuth) {
  QString url = buildUrl(endpoint, queryParams, pathParams);
  QNetworkRequest request(url);

  // 设置默认请求头
  request.setHeader(QNetworkRequest::ContentTypeHeader, "application/json");
  request.setRawHeader("Accept", "application/json");
  request.setRawHeader("User-Agent", "AiWuu-Classroom/1.0");

  // 设置请求超时时间（从配置文件读取）
  request.setTransferTimeout(NETWORK_TIMEOUT_MS);

  // 添加自定义请求头
  for (auto it = headers.begin(); it != headers.end(); ++it) {
    request.setRawHeader(it.key().toUtf8(), it.value().toUtf8());
  }

  // 根据requireAuth参数决定是否添加认证token
  if (requireAuth) {
    QString token = TokenManager::getInstance().getToken();
    if (!token.isEmpty()) {
      request.setRawHeader("Authorization",
                           QString("Bearer %1").arg(token).toUtf8());
      Logger::getInstance().log(Logger::Debug, "已添加Authorization头");
    } else {
      Logger::getInstance().log(Logger::Warning, "需要认证但token为空");
    }
  } else {
    Logger::getInstance().log(Logger::Debug,
                              "接口不需要认证，跳过Authorization头");
  }

  // 记录请求信息
  Logger::getInstance().log(Logger::Debug, QString("发送请求: %1").arg(url));

  // 应用请求拦截器
  if (!applyRequestInterceptors(request)) {
    Logger::getInstance().log(Logger::Warning,
                              QString("请求被拦截器拒绝: %1").arg(url));
  }

  return request;
}

bool NetworkManager::applyRequestInterceptors(QNetworkRequest &request) {
  for (auto interceptor : requestInterceptors) {
    if (!interceptor->interceptRequest(request)) {
      return false;
    }
  }
  return true;
}

bool NetworkManager::applyResponseInterceptors(QNetworkReply *reply,
                                               QJsonObject &response) {
  for (auto interceptor : responseInterceptors) {
    if (!interceptor->interceptResponse(reply, response)) {
      return false;
    }
  }
  return true;
}

void NetworkManager::handleResponse(
    QNetworkReply *reply, std::function<void(const QJsonObject &)> onSuccess,
    std::function<void(const QString &)> onError, int retryCount) {

  // 记录请求的详细信息
  Logger::getInstance().log(Logger::Debug,
    QString("处理响应: URL=%1, 错误=%2, HTTP状态=%3")
      .arg(reply->url().toString())
      .arg(reply->error())
      .arg(reply->attribute(QNetworkRequest::HttpStatusCodeAttribute).toInt()));

  if (reply->error() == QNetworkReply::NoError) {
    QByteArray response = reply->readAll();

    // 直接记录原始响应
    Logger::getInstance().log(
        Logger::Debug, QString("原始响应内容: %1").arg(QString(response)));

    // 解析 JSON 响应
    QJsonObject obj = QJsonDocument::fromJson(response).object();

    // 检查接口返回的业务状态码
    if (obj.contains("code")) {
      int code = obj["code"].toInt();
      if (code != 200) {
        // 接口返回错误，使用 msg 字段作为错误信息
        QString msg = obj.value("msg").toString();
        if (msg.isEmpty()) {
          msg = QString("接口错误，状态码：%1").arg(code);
        }

        Logger::getInstance().log(
            Logger::Warning,
            QString("接口返回错误，code: %1, msg: %2").arg(code).arg(msg));

        // 使用 Toast 显示错误信息
        Toast::error(msg);

        if (onError) {
          onError(msg);
        }
        reply->deleteLater();
        return;
      }
    }

    // 成功响应，调用成功回调
    if (onSuccess) {
      onSuccess(obj);
    }
  } else {
    // 处理错误，直接返回错误信息，不进行自动重试
    // 用户可以手动重试，这样更清晰和可控
    if (onError) {
      QString errorMsg;
      switch (reply->error()) {
      case QNetworkReply::ConnectionRefusedError:
        errorMsg = "连接被拒绝，请检查服务器是否运行";
        break;
      case QNetworkReply::RemoteHostClosedError:
        errorMsg = "服务器关闭了连接";
        break;
      case QNetworkReply::HostNotFoundError:
        errorMsg = "找不到服务器，请检查网络连接";
        break;
      case QNetworkReply::TimeoutError:
        errorMsg = "请求超时，请稍后重试";
        break;
      case QNetworkReply::OperationCanceledError:
        errorMsg = "请求被取消";
        break;
      case QNetworkReply::SslHandshakeFailedError:
        errorMsg = "SSL 握手失败";
        break;
      case QNetworkReply::TemporaryNetworkFailureError:
        errorMsg = "临时网络故障，请稍后重试";
        break;
      case QNetworkReply::NetworkSessionFailedError:
        errorMsg = "网络会话失败";
        break;
      case QNetworkReply::BackgroundRequestNotAllowedError:
        errorMsg = "后台请求不被允许";
        break;
      case QNetworkReply::TooManyRedirectsError:
        errorMsg = "重定向次数过多";
        break;
      case QNetworkReply::InsecureRedirectError:
        errorMsg = "不安全的重定向";
        break;
      case QNetworkReply::UnknownServerError:
        errorMsg = "服务器未知错误";
        break;
      case QNetworkReply::ProxyConnectionRefusedError:
        errorMsg = "代理服务器拒绝连接";
        break;
      case QNetworkReply::ProxyConnectionClosedError:
        errorMsg = "代理服务器关闭了连接";
        break;
      case QNetworkReply::ProxyNotFoundError:
        errorMsg = "找不到代理服务器";
        break;
      case QNetworkReply::ProxyTimeoutError:
        errorMsg = "代理服务器超时";
        break;
      case QNetworkReply::ProxyAuthenticationRequiredError:
        errorMsg = "代理服务器需要认证";
        break;
      case QNetworkReply::ContentAccessDenied:
        errorMsg = "内容访问被拒绝";
        break;
      case QNetworkReply::ContentOperationNotPermittedError:
        errorMsg = "内容操作不被允许";
        break;
      case QNetworkReply::ContentNotFoundError:
        errorMsg = "内容未找到";
        break;
      case QNetworkReply::AuthenticationRequiredError:
        errorMsg = "需要认证";
        break;
      case QNetworkReply::ContentReSendError:
        errorMsg = "内容重发错误";
        break;
      case QNetworkReply::ServiceUnavailableError:
        errorMsg = "服务不可用";
        break;
      default:
        errorMsg = QString("网络错误: %1").arg(reply->errorString());
      }
      onError(errorMsg);
    }
  }

  reply->deleteLater();
}

void NetworkManager::setApiPrefix(const QString &prefix) { apiPrefix = prefix; }

QString NetworkManager::getApiPrefix() const { return apiPrefix; }

QString NetworkManager::buildUrl(const QString &endpoint,
                                 const QMap<QString, QString> &queryParams,
                                 const QMap<QString, QString> &pathParams) {
  QString url = getBaseUrl();

  // 如果 endpoint 不是以 /api 开头，则添加 apiPrefix
  if (!endpoint.startsWith("/api")) {
    url += apiPrefix;
  }
  url += endpoint;

  // 替换路径参数
  for (auto it = pathParams.begin(); it != pathParams.end(); ++it) {
    url.replace(QString("{%1}").arg(it.key()), it.value());
  }

  // 添加查询参数
  if (!queryParams.isEmpty()) {
    QUrlQuery query;
    for (auto it = queryParams.begin(); it != queryParams.end(); ++it) {
      query.addQueryItem(it.key(), it.value());
    }
    url += "?" + query.toString();
  }

  return url;
}

QNetworkReply *NetworkManager::get(
    const QString &endpoint, const QMap<QString, QString> &queryParams,
    const QMap<QString, QString> &pathParams,
    std::function<void(const QJsonObject &)> onSuccess,
    std::function<void(const QString &)> onError, bool requireAuth) {
  QNetworkRequest request =
      createRequest(endpoint, queryParams, pathParams, requireAuth);
  request.setAttribute(QNetworkRequest::User, "GET");
  QNetworkReply *reply = manager->get(request);

  connect(reply, &QNetworkReply::finished,
          [=]() { handleResponse(reply, onSuccess, onError); });

  return reply;
}

QNetworkReply *
NetworkManager::post(const QString &endpoint, const QJsonObject &data,
                     const QMap<QString, QString> &queryParams,
                     const QMap<QString, QString> &pathParams,
                     std::function<void(const QJsonObject &)> onSuccess,
                     std::function<void(const QString &)> onError,
                     bool requireAuth) {
  QNetworkRequest request =
      createRequest(endpoint, queryParams, pathParams, requireAuth);
  request.setAttribute(QNetworkRequest::User, "POST");
  QByteArray jsonData = QJsonDocument(data).toJson();

  QNetworkReply *reply = manager->post(request, jsonData);

  connect(reply, &QNetworkReply::finished,
          [=]() { handleResponse(reply, onSuccess, onError); });

  return reply;
}

QNetworkReply *
NetworkManager::put(const QString &endpoint, const QJsonObject &data,
                    const QMap<QString, QString> &queryParams,
                    const QMap<QString, QString> &pathParams,
                    std::function<void(const QJsonObject &)> onSuccess,
                    std::function<void(const QString &)> onError,
                    bool requireAuth) {
  QNetworkRequest request =
      createRequest(endpoint, queryParams, pathParams, requireAuth);
  request.setAttribute(QNetworkRequest::User, "PUT");
  QByteArray jsonData = QJsonDocument(data).toJson();

  QNetworkReply *reply = manager->put(request, jsonData);

  connect(reply, &QNetworkReply::finished,
          [=]() { handleResponse(reply, onSuccess, onError); });

  return reply;
}

QNetworkReply *NetworkManager::del(
    const QString &endpoint, const QMap<QString, QString> &queryParams,
    const QMap<QString, QString> &pathParams,
    std::function<void(const QJsonObject &)> onSuccess,
    std::function<void(const QString &)> onError, bool requireAuth) {
  QNetworkRequest request =
      createRequest(endpoint, queryParams, pathParams, requireAuth);
  request.setAttribute(QNetworkRequest::User, "DELETE");

  QNetworkReply *reply = manager->deleteResource(request);

  connect(reply, &QNetworkReply::finished,
          [=]() { handleResponse(reply, onSuccess, onError); });

  return reply;
}

void NetworkManager::setAuthToken(const QString &token) {
  authToken = token;
  TokenManager::getInstance().setToken(token);
}