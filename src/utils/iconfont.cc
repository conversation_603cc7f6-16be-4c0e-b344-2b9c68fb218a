#include "utils/iconfont.h"
#include <QDebug>
#include <QPainter>
#include <QPixmap>

IconFont& IconFont::getInstance()
{
    static IconFont instance;
    return instance;
}

IconFont::IconFont(QObject *parent)
    : QObject(parent)
    , fontId(-1)
{
    // 初始化图标映射
    iconMap["user"] = 0xe7ae;        // 用户图标
    iconMap["password"] = 0xe7b3;    // 密码图标
    iconMap["home"] = 0xe7c6;        // 首页图标
    iconMap["setting"] = 0xe78e;     // 设置图标
    iconMap["logout"] = 0xe7c9;      // 退出图标
    iconMap["search"] = 0xe8ef;      // 搜索图标
    iconMap["edit"] = 0xe7e3;        // 编辑图标
    iconMap["delete"] = 0xe7c3;      // 删除图标
    iconMap["add"] = 0xe7b9;         // 添加图标
    iconMap["save"] = 0xe7be;        // 保存图标
}

IconFont::~IconFont()
{
    if (fontId != -1) {
        QFontDatabase::removeApplicationFont(fontId);
    }
}

void IconFont::initFont(const QString& fontPath)
{
    // 加载字体文件
    fontId = QFontDatabase::addApplicationFont(fontPath);
    if (fontId == -1) {
        qWarning() << "Failed to load icon font:" << fontPath;
    }
}

QIcon IconFont::getIcon(const QString& iconName, int size, const QColor& color)
{
    if (!iconMap.contains(iconName)) {
        qWarning() << "Icon not found:" << iconName;
        return QIcon();
    }

    // 创建图标
    QPixmap pixmap(size, size);
    pixmap.fill(Qt::transparent);

    QPainter painter(&pixmap);
    painter.setRenderHint(QPainter::Antialiasing);
    painter.setRenderHint(QPainter::TextAntialiasing);

    // 设置字体
    QFont font = getFont(size);
    painter.setFont(font);
    painter.setPen(color);

    // 绘制图标
    painter.drawText(pixmap.rect(), Qt::AlignCenter, QChar(iconMap[iconName]));

    return QIcon(pixmap);
}

QFont IconFont::getFont(int size)
{
    QFont font;
    if (fontId != -1) {
        font = QFont(QFontDatabase::applicationFontFamilies(fontId).first());
        font.setPixelSize(size);
    }
    return font;
} 