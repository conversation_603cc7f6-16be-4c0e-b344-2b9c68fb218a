#include "utils/logger.h"
#include <QCoreApplication>
#include <QDateTime>
#include <QDebug>
#include <QDir>
#include <QFileInfo>

// 定义日志类别
Q_LOGGING_CATEGORY(logDebug, "app.debug")
Q_LOGGING_CATEGORY(logInfo, "app.info")
Q_LOGGING_CATEGORY(logWarning, "app.warning")
Q_LOGGING_CATEGORY(logError, "app.error")

Logger *Logger::instance = nullptr;

Logger &Logger::getInstance() {
  if (!instance) {
    instance = new Logger();
  }
  return *instance;
}

Logger::Logger(QObject *parent)
    : QObject(parent), logFile(nullptr), logStream(nullptr) {
  // 设置默认配置
  config.maxDays = 30;
  config.maxFileSize = 10 * 1024 * 1024; // 10MB
  config.maxBackupFiles = 10;
  config.pattern = "%d{yyyy-MM-dd HH:mm:ss.zzz} [%p] %c - %m%n";
  config.level = Debug;
}

Logger::~Logger() {
  if (logStream) {
    delete logStream;
  }
  if (logFile) {
    logFile->close();
    delete logFile;
  }
}

void Logger::init(const QString &logPath, const Config &config) {
  this->config = config;

  // 如果没有指定日志路径，使用应用程序目录下的logs文件夹
  if (logPath.isEmpty()) {
    QString appPath = QCoreApplication::applicationDirPath();
    this->logPath = appPath + "/logs";
    qDebug() << "日志目录：" << this->logPath;
  } else {
    this->logPath = logPath;
  }

  // 确保日志目录存在
  createLogDirectory(this->logPath);

  // 打开日志文件
  if (logFile) {
    logFile->close();
    delete logFile;
  }
  if (logStream) {
    delete logStream;
  }

  QString logFileName = getLogFileName();
  qDebug() << "日志文件：" << logFileName;

  logFile = new QFile(logFileName);
  if (!logFile->open(QIODevice::WriteOnly | QIODevice::Append |
                     QIODevice::Text)) {
    qWarning() << "无法打开日志文件：" << logFileName;
    return;
  }

  logStream = new QTextStream(logFile);

  // 安装消息处理器
  qInstallMessageHandler(messageHandler);

  // 记录初始化日志
  log(Info, "日志系统初始化完成");
}

void Logger::setLogLevel(LogLevel level) { config.level = level; }

void Logger::setConfig(const Config &config) { this->config = config; }

Logger::Config Logger::getConfig() const { return config; }

void Logger::cleanOldLogs() {
  QDir dir(logPath);
  if (!dir.exists()) {
    return;
  }

  QFileInfoList fileList =
      dir.entryInfoList(QStringList() << "*.log", QDir::Files, QDir::Time);
  QDateTime currentDate = QDateTime::currentDateTime();

  for (const QFileInfo &fileInfo : fileList) {
    QDateTime fileDate = fileInfo.lastModified();
    if (fileDate.daysTo(currentDate) > config.maxDays) {
      QFile::remove(fileInfo.absoluteFilePath());
    }
  }
}

void Logger::createLogDirectory(const QString &path) {
  QDir dir(path);
  if (!dir.exists()) {
    dir.mkpath(".");
  }
}

QString Logger::getLogFileName() const {
  QString dateStr = QDateTime::currentDateTime().toString("yyyy_MM_dd");
  return QString("%1/app_%2.log").arg(logPath).arg(dateStr);
}

void Logger::checkAndRotateLogFile() {
  if (!logFile || !logFile->isOpen()) {
    return;
  }

  // 检查文件大小
  if (logFile->size() >= config.maxFileSize) {
    // 关闭当前文件
    logFile->close();

    // 重命名现有备份文件
    QString baseName = getLogFileName();
    for (int i = config.maxBackupFiles - 1; i >= 0; --i) {
      QString oldName =
          i == 0 ? baseName : QString("%1.%2").arg(baseName).arg(i);
      QString newName = QString("%1.%2").arg(baseName).arg(i + 1);

      if (QFile::exists(oldName)) {
        if (i == config.maxBackupFiles - 1) {
          QFile::remove(oldName);
        } else {
          QFile::rename(oldName, newName);
        }
      }
    }

    // 重新打开文件
    logFile->open(QIODevice::WriteOnly | QIODevice::Append | QIODevice::Text);
  }
}

void Logger::messageHandler(QtMsgType type, const QMessageLogContext &context,
                            const QString &msg) {
  if (!instance || !instance->logStream) {
    return;
  }

  // 检查日志级别
  if (type < instance->config.level) {
    return;
  }

  // 检查并轮转日志文件
  instance->checkAndRotateLogFile();

  QString txt;
  switch (type) {
  case QtDebugMsg:
    txt = QString("Debug");
    break;
  case QtInfoMsg:
    txt = QString("Info");
    break;
  case QtWarningMsg:
    txt = QString("Warning");
    break;
  case QtCriticalMsg:
    txt = QString("Critical");
    break;
  case QtFatalMsg:
    txt = QString("Fatal");
    break;
  }

  QString message =
      QString("%1 [%2] %3 - %4")
          .arg(QDateTime::currentDateTime().toString("yyyy-MM-dd HH:mm:ss.zzz"))
          .arg(txt)
          .arg(context.category)
          .arg(msg);

  // 写入日志文件
  *instance->logStream << message << Qt::endl;
  instance->logStream->flush();

  // 同时输出到控制台
  fprintf(stderr, "%s\n", message.toLocal8Bit().constData());

  // 如果是致命错误，终止程序
  if (type == QtFatalMsg) {
    abort();
  }
}

void Logger::log(LogLevel level, const QString &message) {
  // 检查日志级别
  if (level < config.level) {
    return;
  }

  // 检查并轮转日志文件
  checkAndRotateLogFile();

  QString levelStr;
  switch (level) {
  case Debug:
    levelStr = "Debug";
    break;
  case Info:
    levelStr = "Info";
    break;
  case Warning:
    levelStr = "Warning";
    break;
  case Error:
    levelStr = "Error";
    break;
  case Fatal:
    levelStr = "Fatal";
    break;
  }

  QString formattedMessage =
      QString("%1 [%2] %3")
          .arg(QDateTime::currentDateTime().toString("yyyy-MM-dd HH:mm:ss.zzz"))
          .arg(levelStr)
          .arg(message);

  // 写入日志文件
  if (logStream) {
    *logStream << formattedMessage << Qt::endl;
    logStream->flush();
  }

  // 同时输出到控制台
  fprintf(stderr, "%s\n", formattedMessage.toLocal8Bit().constData());

  // 如果是致命错误，终止程序
  if (level == Fatal) {
    abort();
  }
}