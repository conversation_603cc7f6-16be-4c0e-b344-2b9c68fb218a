add_library(utils
    formutils.cc
    storage.cc
    tokenmanager.cc
    networkmanager.cc
)

target_include_directories(utils
    PUBLIC
        ${CMAKE_SOURCE_DIR}/include
        ${CMAKE_SOURCE_DIR}/include/core
        ${CMAKE_SOURCE_DIR}/include/utils
        ${CMAKE_SOURCE_DIR}/include/api
    PRIVATE
        ${CMAKE_CURRENT_SOURCE_DIR}
)

target_link_libraries(utils
    PUBLIC
        Qt5::Core
        Qt5::Network
    PRIVATE
        api
)