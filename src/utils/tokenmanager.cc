#include "utils/tokenmanager.h"
#include "utils/storage.h"

#include <QCoreApplication>
#include <QDir>
#include <QJsonDocument>
#include <QJsonObject>
#include <QDebug>

const QString TokenManager::TOKEN_KEY = "auth_token";
const QString TokenManager::EXPIRATION_KEY = "token_expiration";

TokenManager& TokenManager::getInstance()
{
    static TokenManager instance;
    return instance;
}

TokenManager::TokenManager(QObject* parent)
    : QObject(parent)
{
    // 初始化 QSettings，使用应用程序名称作为组织名
    settings = new QSettings(QCoreApplication::organizationName(),
                           QCoreApplication::applicationName(),
                           this);
}

TokenManager::~TokenManager()
{
    delete settings;
}

void TokenManager::setToken(const QString& token, int expiresIn)
{
    settings->setValue(TOKEN_KEY, token);
    
    if (expiresIn > 0) {
        QDateTime expirationTime = QDateTime::currentDateTime().addSecs(expiresIn);
        settings->setValue(EXPIRATION_KEY, expirationTime);
    } else {
        settings->remove(EXPIRATION_KEY);
    }
    
    settings->sync(); // 确保立即写入磁盘
}

QString TokenManager::getToken() const
{
    if (isTokenExpired()) {
        return QString();
    }
    return settings->value(TOKEN_KEY).toString();
}

void TokenManager::clearToken()
{
    settings->remove(TOKEN_KEY);
    settings->remove(EXPIRATION_KEY);
    settings->sync();
}

bool TokenManager::hasToken() const
{
    return !getToken().isEmpty();
}

bool TokenManager::isTokenExpired() const
{
    if (!settings->contains(EXPIRATION_KEY)) {
        return false;
    }
    
    QDateTime expirationTime = settings->value(EXPIRATION_KEY).toDateTime();
    return QDateTime::currentDateTime() >= expirationTime;
}

QDateTime TokenManager::getExpirationTime() const
{
    return settings->value(EXPIRATION_KEY).toDateTime();
} 