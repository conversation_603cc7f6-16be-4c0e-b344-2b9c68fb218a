#include "utils/formutils.h"
#include "utils/toast.h"

#include <QCheckBox>
#include <QComboBox>
#include <QDateEdit>
#include <QDateTimeEdit>
#include <QDebug>
#include <QDoubleSpinBox>
#include <QGroupBox>
#include <QLineEdit>
#include <QRadioButton>
#include <QSpinBox>
#include <QTextEdit>
#include <QWidget>

void FormUtils::resetLineEdit(QLineEdit *edit) {
  if (edit) {
    edit->clear();
    edit->setFocus();
  }
}

void FormUtils::resetLineEdits(const QList<QLineEdit *> &edits) {
  for (QLineEdit *edit : edits) {
    resetLineEdit(edit);
  }
}

void FormUtils::resetTextEdit(QTextEdit *edit) {
  if (edit) {
    edit->clear();
    edit->setFocus();
  }
}

void FormUtils::resetComboBox(QComboBox *combo) {
  if (combo) {
    combo->setCurrentIndex(0);
  }
}

void FormUtils::resetCheckBox(QCheckBox *checkBox) {
  if (checkBox) {
    checkBox->setChecked(false);
  }
}

void FormUtils::resetRadioButton(QRadioButton *radio) {
  if (radio) {
    radio->setChecked(false);
  }
}

void FormUtils::resetSpinBox(QSpinBox *spinBox) {
  if (spinBox) {
    spinBox->setValue(spinBox->minimum());
  }
}

void FormUtils::resetDoubleSpinBox(QDoubleSpinBox *spinBox) {
  if (spinBox) {
    spinBox->setValue(spinBox->minimum());
  }
}

void FormUtils::resetDateEdit(QDateEdit *dateEdit) {
  if (dateEdit) {
    dateEdit->setDate(QDate::currentDate());
  }
}

void FormUtils::resetTimeEdit(QTimeEdit *timeEdit) {
  if (timeEdit) {
    timeEdit->setTime(QTime::currentTime());
  }
}

void FormUtils::resetDateTimeEdit(QDateTimeEdit *dateTimeEdit) {
  if (dateTimeEdit) {
    dateTimeEdit->setDateTime(QDateTime::currentDateTime());
  }
}

void FormUtils::resetForm(const QList<QWidget *> &widgets) {
  for (QWidget *widget : widgets) {
    if (QLineEdit *lineEdit = qobject_cast<QLineEdit *>(widget)) {
      lineEdit->clear();
    } else if (QTextEdit *textEdit = qobject_cast<QTextEdit *>(widget)) {
      textEdit->clear();
    } else if (QComboBox *comboBox = qobject_cast<QComboBox *>(widget)) {
      comboBox->setCurrentIndex(0);
    } else if (QSpinBox *spinBox = qobject_cast<QSpinBox *>(widget)) {
      spinBox->setValue(spinBox->minimum());
    } else if (QDoubleSpinBox *doubleSpinBox =
                   qobject_cast<QDoubleSpinBox *>(widget)) {
      doubleSpinBox->setValue(doubleSpinBox->minimum());
    } else if (QDateEdit *dateEdit = qobject_cast<QDateEdit *>(widget)) {
      dateEdit->setDate(QDate::currentDate());
    } else if (QDateTimeEdit *dateTimeEdit =
                   qobject_cast<QDateTimeEdit *>(widget)) {
      dateTimeEdit->setDateTime(QDateTime::currentDateTime());
    } else if (QCheckBox *checkBox = qobject_cast<QCheckBox *>(widget)) {
      checkBox->setChecked(false);
    } else if (QRadioButton *radioButton =
                   qobject_cast<QRadioButton *>(widget)) {
      radioButton->setChecked(false);
    } else if (QGroupBox *groupBox = qobject_cast<QGroupBox *>(widget)) {
      groupBox->setChecked(false);
    }
  }
}

bool FormUtils::validateRequired(const QString &value,
                                 const QString &fieldName) {
  if (value.trimmed().isEmpty()) {
    showError(QString("%1不能为空").arg(fieldName));
    return false;
  }
  return true;
}

bool FormUtils::validateLength(const QString &value, int minLength,
                               int maxLength, const QString &fieldName) {
  int length = value.length();
  if (length < minLength || length > maxLength) {
    showError(QString("%1长度必须在%2-%3个字符之间")
                  .arg(fieldName)
                  .arg(minLength)
                  .arg(maxLength));
    return false;
  }
  return true;
}

bool FormUtils::validateEmail(const QString &email) {
  QRegExp emailRegex("^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$");
  if (!emailRegex.exactMatch(email)) {
    showError("邮箱格式不正确");
    return false;
  }
  return true;
}

bool FormUtils::validatePhone(const QString &phone) {
  QRegExp phoneRegex("^1[3-9]\\d{9}$");
  if (!phoneRegex.exactMatch(phone)) {
    showError("手机号格式不正确");
    return false;
  }
  return true;
}

bool FormUtils::validateCustom(const QString &value, const QString &fieldName,
                               std::function<bool(const QString &)> validator,
                               const QString &errorMessage) {
  if (!validator(value)) {
    showError(errorMessage.isEmpty() ? QString("%1验证失败").arg(fieldName)
                                     : errorMessage);
    return false;
  }
  return true;
}

bool FormUtils::validateWithRules(
    const QString &value, const QString &fieldName,
    const QList<std::function<bool(const QString &)>> &validators,
    const QList<QString> &errorMessages) {
  for (int i = 0; i < validators.size(); ++i) {
    if (!validators[i](value)) {
      showError(i < errorMessages.size()
                    ? errorMessages[i]
                    : QString("%1验证失败").arg(fieldName));
      return false;
    }
  }
  return true;
}

bool FormUtils::validateForm(
    const QMap<QString, QString> &formData,
    const QMap<QString, QList<std::function<bool(const QString &)>>> &rules,
    const QMap<QString, QList<QString>> &errorMessages) {
  for (auto it = formData.begin(); it != formData.end(); ++it) {
    const QString &fieldName = it.key();
    const QString &value = it.value();

    if (rules.contains(fieldName)) {
      const auto &fieldRules = rules[fieldName];
      const auto &fieldErrors = errorMessages[fieldName];

      for (int i = 0; i < fieldRules.size(); ++i) {
        if (!fieldRules[i](value)) {
          showError(i < fieldErrors.size()
                        ? fieldErrors[i]
                        : QString("%1验证失败").arg(fieldName));
          return false;
        }
      }
    }
  }
  return true;
}

void FormUtils::showError(const QString &message) {
  Toast::error(message, nullptr, 2000);
}