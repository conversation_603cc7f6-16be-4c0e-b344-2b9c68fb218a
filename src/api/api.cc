#include "api/api.h"
#include "utils/networkmanager.h"

#include <QDebug>
#include <QJsonDocument>
#include <QJsonObject>
#include <QNetworkReply>

// 定义接口常量
const QString Api::LOGIN = "/auth/classRoomLogin";
const QString Api::REGISTER = "/auth/register";
const QString Api::GET_USER_INFO = "/user/info";
const QString Api::UPDATE_USER_INFO = "/user/update";
const QString Api::LOGOUT = "/auth/logout";

// 初始化 NetworkManager 引用
NetworkManager &Api::networkManager = NetworkManager::getInstance();

// 用户相关方法实现
QNetworkReply *Api::login(const QString &username, const QString &password) {
  QJsonObject data;
  data["username"] = username;
  data["password"] = password;
  return networkManager.post(LOGIN, data);
}

QNetworkReply *Api::registerUser(const QString &username,
                                 const QString &password,
                                 const QString &email) {
  QJsonObject data;
  data["username"] = username;
  data["password"] = password;
  data["email"] = email;
  return networkManager.post(REGISTER, data);
}

QNetworkReply *Api::getUserInfo() { return networkManager.get(GET_USER_INFO); }

QNetworkReply *Api::updateUserInfo(const QJsonObject &userInfo) {
  return networkManager.put(UPDATE_USER_INFO, userInfo);
}

QNetworkReply *Api::logout() {
  // 使用新的NetworkManager接口，提供空的回调函数
  return networkManager.post(LOGOUT, QJsonObject(),
                             QMap<QString, QString>(), // queryParams
                             QMap<QString, QString>(), // pathParams
                             nullptr,                  // onSuccess
                             nullptr,                  // onError
                             true);                    // requireAuth
}
