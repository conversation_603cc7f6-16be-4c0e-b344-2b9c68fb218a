#!/bin/bash

# Qt Designer插件检查脚本
# 帮助用户检查和启用Qt Designer插件

echo "🔍 Qt Designer插件检查指南"
echo "================================"
echo ""

echo "📋 在Qt Creator中检查Qt Designer插件："
echo ""
echo "方法1: 通过帮助菜单检查"
echo "1. 打开Qt Creator"
echo "2. 点击菜单栏 'Help' -> 'About Plugins...' (或 '帮助' -> '关于插件...')"
echo "3. 在插件列表中查找以下插件："
echo "   - Qt Designer (设计器相关)"
echo "   - Designer (UI设计器)"
echo "   - Qt Quick Designer (如果使用QML)"
echo "4. 确保这些插件前面的复选框已勾选 ✅"
echo "5. 如果未勾选，请勾选并重启Qt Creator"
echo ""

echo "方法2: 通过工具菜单检查"
echo "1. 打开Qt Creator"
echo "2. 点击菜单栏 'Tools' (或 '工具')"
echo "3. 查看是否有 'Form Editor' 或 '表单编辑器' 选项"
echo "4. 如果没有此选项，说明Designer插件未启用"
echo ""

echo "方法3: 通过文件关联检查"
echo "1. 在Qt Creator项目树中找到 .ui 文件"
echo "2. 右键点击 .ui 文件"
echo "3. 查看 'Open With' (或 '打开方式') 菜单"
echo "4. 应该看到以下选项："
echo "   - Qt Designer"
echo "   - Plain Text Editor"
echo "5. 如果没有 'Qt Designer' 选项，说明插件未启用"
echo ""

echo "🔧 如何启用Qt Designer插件："
echo ""
echo "步骤1: 打开插件管理器"
echo "1. Qt Creator -> Help -> About Plugins..."
echo "2. 或者 Qt Creator -> Preferences -> Plugins (macOS)"
echo "3. 或者 Tools -> Options -> Plugins (Windows/Linux)"
echo ""

echo "步骤2: 查找并启用Designer插件"
echo "1. 在插件列表中搜索 'Designer' 或 'Qt Designer'"
echo "2. 确保以下插件已启用 (勾选)："
echo "   ✅ Designer"
echo "   ✅ Qt Designer"
echo "   ✅ Qt Quick Designer (可选)"
echo "3. 如果插件显示为灰色或不可用，可能需要："
echo "   - 更新Qt Creator到最新版本"
echo "   - 重新安装Qt Creator"
echo "   - 检查Qt安装是否完整"
echo ""

echo "步骤3: 重启Qt Creator"
echo "1. 点击 'OK' 或 '确定' 保存插件设置"
echo "2. 重启Qt Creator"
echo "3. 重新打开项目"
echo ""

echo "🧪 测试Qt Designer是否工作："
echo ""
echo "测试方法1: 双击.ui文件"
echo "1. 在项目树中双击 ui/loginwindow.ui"
echo "2. 应该打开Qt Designer界面"
echo "3. 可以看到可视化的UI编辑器"
echo ""

echo "测试方法2: 右键菜单"
echo "1. 右键点击 ui/loginwindow.ui"
echo "2. 选择 'Open With' -> 'Qt Designer'"
echo "3. 应该打开设计器界面"
echo ""

echo "测试方法3: 创建新UI文件"
echo "1. 右键点击项目"
echo "2. 选择 'Add New...' -> 'Qt' -> 'Qt Designer Form'"
echo "3. 如果可以创建，说明Designer工作正常"
echo ""

echo "❌ 常见问题和解决方案："
echo ""
echo "问题1: 插件列表中没有Designer插件"
echo "解决方案:"
echo "- 检查Qt Creator版本是否支持Qt Designer"
echo "- 重新安装Qt Creator (选择完整安装)"
echo "- 确保安装了Qt Designer组件"
echo ""

echo "问题2: 插件显示但无法启用"
echo "解决方案:"
echo "- 检查Qt Creator和Qt版本兼容性"
echo "- 以管理员权限运行Qt Creator"
echo "- 清理Qt Creator配置文件重新设置"
echo ""

echo "问题3: 插件已启用但.ui文件无法打开"
echo "解决方案:"
echo "- 检查.ui文件格式是否正确"
echo "- 尝试创建新的.ui文件测试"
echo "- 重新生成项目文件"
echo ""

echo "🔗 相关文件位置："
echo ""
echo "Qt Creator配置目录 (macOS):"
echo "~/Library/Application Support/QtProject/qtcreator/"
echo ""
echo "Qt Creator配置目录 (Windows):"
echo "%APPDATA%/QtProject/qtcreator/"
echo ""
echo "Qt Creator配置目录 (Linux):"
echo "~/.config/QtProject/qtcreator/"
echo ""

echo "💡 提示："
echo "- 如果所有方法都无效，建议重新安装Qt Creator"
echo "- 确保安装时选择了Qt Designer组件"
echo "- 某些精简版Qt Creator可能不包含Designer插件"
echo "- 可以单独安装Qt Designer作为独立应用程序"
echo ""

echo "✅ 检查完成！"
echo "如果按照以上步骤仍无法解决问题，请考虑："
echo "1. 更新到最新版本的Qt Creator"
echo "2. 重新安装Qt开发环境"
echo "3. 使用独立的Qt Designer应用程序" 