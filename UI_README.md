# Qt UI文件使用说明

## 概述

本项目现在支持两种UI开发方式：
1. **纯代码方式**：原有的手写UI代码（推荐用于复杂逻辑）
2. **UI文件方式**：使用Qt Designer可视化编辑（推荐用于快速原型和设计）

## 文件结构

```
ui/
├── loginwindow.ui              # 登录窗口UI文件
├── mainwindow.ui               # 主窗口UI文件

include/core/
├── loginwindow.h               # 纯代码版本头文件
├── loginwindow_ui.h            # UI文件版本头文件
├── mainwindow.h                # 纯代码版本头文件
├── mainwindow_ui.h             # UI文件版本头文件

src/core/
├── loginwindow.cc              # 纯代码版本实现
├── loginwindow_ui.cc           # UI文件版本实现
├── mainwindow.cc               # 纯代码版本实现
├── mainwindow_ui.cc            # UI文件版本实现
├── main.cc                     # 纯代码版本入口
├── main_ui.cc                  # UI文件版本入口（示例）
```

## 在Qt Creator中使用UI文件

### 1. 打开UI文件
- 在Qt Creator的项目树中找到 `ui/loginwindow.ui`
- 双击文件即可在Qt Designer中打开

### 2. 可视化编辑
- **拖拽控件**：从左侧控件面板拖拽到设计区域
- **调整布局**：选中控件后使用布局工具栏
- **设置属性**：在右侧属性面板中修改控件属性
- **预览效果**：使用 `Ctrl+R` 预览界面

### 3. 常用操作
- **添加控件**：从Widget Box拖拽到界面
- **删除控件**：选中后按Delete键
- **复制控件**：Ctrl+C / Ctrl+V
- **撤销操作**：Ctrl+Z
- **保存文件**：Ctrl+S

## 编译和运行

### 使用纯代码版本（默认）
```bash
# 构建项目
./dev.sh

# 运行
./build/aiwuu-classroom
```

### 使用UI文件版本
```bash
# 修改CMakeLists.txt中的main文件
# 将 src/core/main.cc 改为 src/core/main_ui.cc

# 或者创建单独的可执行文件
add_executable(aiwuu-classroom-ui src/core/main_ui.cc ${SOURCES} ${HEADERS} ${UI_FILES} ${RESOURCE_FILES})
```

## 开发建议

### 何时使用UI文件
- ✅ 快速原型设计
- ✅ 静态界面布局
- ✅ 设计师参与的项目
- ✅ 需要频繁调整界面的场景

### 何时使用纯代码
- ✅ 复杂的动态界面
- ✅ 需要精确控制的场景
- ✅ 大量程序化生成的控件
- ✅ 性能敏感的应用

### 混合使用
可以在同一个项目中混合使用两种方式：
- 主界面使用UI文件快速设计
- 复杂组件使用纯代码实现
- 动态内容使用代码添加到UI文件的容器中

## 注意事项

### 1. 对象名称
UI文件中的控件对象名称必须与代码中的引用保持一致：
```cpp
// UI文件中设置objectName为"loginButton"
// 代码中通过ui->loginButton访问
connect(ui->loginButton, &QPushButton::clicked, this, &LoginWindowUI::onLoginClicked);
```

### 2. 样式表
样式表仍然通过QSS文件控制，UI文件主要负责布局和基本属性。

### 3. 信号槽连接
可以在UI文件中设置信号槽连接，也可以在代码中手动连接：
```cpp
// 代码中连接（推荐）
connect(ui->loginButton, &QPushButton::clicked, this, &LoginWindowUI::onLoginClicked);
```

### 4. 版本同步
如果同时维护两个版本，需要确保功能和界面保持同步。

## 最佳实践

### 1. 命名规范
- 控件名称使用驼峰命名：`loginButton`, `usernameEdit`
- 布局名称添加Layout后缀：`mainLayout`, `formLayout`
- 容器名称添加Container后缀：`titleContainer`

### 2. 布局管理
- 优先使用布局管理器而不是固定位置
- 设置合适的尺寸策略（Size Policy）
- 使用Spacer控制间距

### 3. 属性设置
- 在UI文件中设置基本属性（大小、文本等）
- 在代码中设置动态属性和复杂逻辑
- 使用样式表控制外观

## 故障排除

### 编译错误
如果遇到 `ui_loginwindow.h` 找不到的错误：
1. 确保CMakeLists.txt中启用了 `CMAKE_AUTOUIC ON`
2. 确保UI文件被添加到可执行文件中
3. 清理并重新构建项目

### 控件访问错误
如果遇到控件访问错误：
1. 检查UI文件中的objectName设置
2. 确保在setupUi()之后访问控件
3. 检查命名空间是否正确

### 界面显示异常
如果界面显示不正常：
1. 检查布局设置是否正确
2. 确认样式表是否正确加载
3. 检查窗口属性设置 