{"configurations": [{"name": "<PERSON>", "includePath": ["${workspaceFolder}/**", "/opt/homebrew/opt/qt@5/include/**", "${workspaceFolder}/include/**"], "defines": [], "macFrameworkPath": ["/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks"], "compilerPath": "/usr/bin/clang", "cStandard": "c17", "cppStandard": "c++17", "intelliSenseMode": "macos-clang-arm64"}], "version": 4}