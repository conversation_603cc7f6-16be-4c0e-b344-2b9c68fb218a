{"version": "0.2.0", "configurations": [{"name": "Debug", "type": "cppdbg", "request": "launch", "program": "${workspaceFolder}/aiwuu", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}", "environment": [], "externalConsole": false, "MIMode": "lldb", "preLaunchTask": "make", "visualizerFile": "/Users/<USER>/Library/Application Support/Code/User/workspaceStorage/a9eb49b17739986b11940c4357894add/tonka3000.qtvsctools/qt.natvis.xml"}]}