{"version": "2.0.0", "tasks": [{"label": "qmake", "type": "shell", "command": "qmake", "group": "build"}, {"label": "make", "type": "shell", "command": "make", "group": "build", "dependsOn": ["qmake"]}, {"label": "dev", "type": "shell", "command": "./dev.sh", "args": ["--env=dev"], "group": {"kind": "test", "isDefault": true}}, {"label": "prod", "type": "shell", "command": "./dev.sh", "args": ["--env=prod"], "group": "test"}, {"label": "clean", "type": "shell", "command": "make clean", "group": "build"}, {"type": "cppbuild", "label": "C/C++: clang++ 生成活动文件", "command": "/usr/bin/clang++", "args": ["-fcolor-diagnostics", "-fansi-escape-codes", "-g", "${file}", "-o", "${fileDirname}/${fileBasenameNoExtension}"], "options": {"cwd": "${fileDirname}"}, "problemMatcher": ["$gcc"], "group": {"kind": "build", "isDefault": true}, "detail": "调试器生成的任务。"}]}