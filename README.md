# AIWUU Classroom

AIWUU Classroom 是一个基于 Qt 5.10 开发的桌面应用程序。

## 环境要求

- Qt 5.10 或更高版本
- C++ 编译器（支持 C++11）
- VSCode（推荐）或其他 IDE

## 图片优化工具安装

### macOS

```bash
# 安装图片优化工具
brew install pngquant jpegoptim

# 验证安装
pngquant --version
jpegoptim --version
```

### Linux (Ubuntu/Debian)

```bash
# 安装图片优化工具
sudo apt-get update
sudo apt-get install pngquant jpegoptim

# 验证安装
pngquant --version
jpegoptim --version
```

### Windows

1. **安装 ImageMagick**：

   - 访问 [ImageMagick 官网](https://imagemagick.org/script/download.php#windows)
   - 下载 Windows 安装包
   - 运行安装程序，确保选中"Add application directory to your system path"选项
   - 验证安装：
     ```bash
     magick --version
     ```

2. **替代方案**：
   - 如果不想安装 ImageMagick，项目会自动使用 Qt 内置的图片优化功能
   - 也可以使用其他 Windows 图片优化工具：
     - [FileOptimizer](https://nikkhokkho.sourceforge.io/static.php?page=FileOptimizer)
     - [Caesium](https://saerasoft.com/caesium/)

### 注意事项

1. 图片优化工具是可选的，但建议安装以获得更好的优化效果
2. 如果没有安装优化工具，项目会使用 Qt 内置的优化功能
3. 优化工具主要用于打包时的资源优化，不影响开发过程

## 项目结构

```
aiwuu-classroom/
├── .vscode/              # VSCode 配置文件
├── include/             # 头文件目录
│   ├── core/           # 核心模块头文件
│   │   ├── mainwindow.h
│   │   └── loginwindow.h
│   ├── utils/          # 工具类头文件
│   │   ├── networkmanager.h
│   │   ├── messagebox.h
│   │   ├── formutils.h
│   │   ├── storage.h
│   │   ├── tokenmanager.h
│   │   └── iconfont.h
│   └── api/            # API 接口头文件
│       └── api.h
├── src/                # 源文件目录
│   ├── core/           # 核心模块源文件
│   │   ├── main.cc
│   │   ├── mainwindow.cc
│   │   └── loginwindow.cc
│   ├── utils/          # 工具类源文件
│   │   ├── networkmanager.cc
│   │   ├── messagebox.cc
│   │   ├── formutils.cc
│   │   ├── storage.cc
│   │   ├── tokenmanager.cc
│   │   └── iconfont.cc
│   └── api/            # API 接口源文件
│       └── api.cc
├── resources/          # 资源文件目录
│   ├── images/        # 图片资源
│   ├── fonts/         # 字体资源
│   ├── styles/        # 样式文件
│   └── resources.qrc  # Qt 资源文件
├── config.dev.h        # 开发环境配置
├── config.prod.h       # 生产环境配置
├── dev.sh             # 开发脚本
├── build.sh           # 打包脚本
└── aiwuu.pro          # Qt 项目文件
```

### 目录说明

1. **include/**：所有头文件

   - `core/`：核心模块头文件
   - `utils/`：工具类头文件
   - `api/`：API 接口头文件

2. **src/**：所有源文件

   - `core/`：核心模块源文件
   - `utils/`：工具类源文件
   - `api/`：API 接口源文件

3. **resources/**：资源文件

   - `images/`：图片资源
   - `fonts/`：字体资源
   - `styles/`：样式文件
   - `resources.qrc`：Qt 资源文件

4. **配置文件**

   - `config.dev.h`：开发环境配置
   - `config.prod.h`：生产环境配置
   - `aiwuu.pro`：Qt 项目文件

5. **脚本文件**
   - `dev.sh`：开发脚本
   - `build.sh`：打包脚本

### 文件命名规范

1. 所有 C++ 源文件使用 `.cc` 后缀
2. 所有头文件使用 `.h` 后缀
3. 文件名使用小写字母，单词之间用下划线分隔
4. 类名使用大驼峰命名法

## 快速开始

### 命令行方式

1. 开发环境构建和运行：

```bash
./dev.sh
```

2. 生产环境构建（Windows 平台）：

```bash
./build.sh -p windows -e prod
```

3. 生产环境构建（Linux 平台）：

```bash
./build.sh -p linux -e prod
```

4. 环境切换（开发/生产）:

```bash
# 开发环境
./dev.sh --env=dev

# 生产环境
./dev.sh --env=prod
```

### VSCode 方式

1. 打开命令面板（Cmd+Shift+P）
2. 输入 "Tasks: Run Task"
3. 选择以下任务之一：
   - `dev`: 运行开发环境
   - `prod`: 运行生产环境
   - `clean`: 清理构建文件

### Qt Creator 方式

Qt Creator 可以直接打开 CMakeLists.txt 文件，并自动配置项目。如果需要使用自定义构建脚本，请按照以下步骤手动配置：

#### 🔧 Qt Creator 手动配置指南

当你在 Qt Creator 中打开项目时，会看到 'Configure Project' 页面，这是正常的。请按照以下步骤配置：

**📋 步骤1: 配置Kit**
1. 在 'Configure Project' 页面中
2. 确保选中 'Desktop Qt 5.10.1 MSVC2017 64bit' (或你的Qt版本)
3. 点击右下角的 'Configure Project' 按钮

**📋 步骤2: 配置构建设置**
配置完成后，你会看到项目界面，然后：
1. 点击左侧的 '项目' 按钮 (或按 Ctrl+5)
2. 在 'Build & Run' 下选择你的Kit
3. 在 'Build Settings' 中：
   - 修改 'Build directory' 为: `%{sourceDir}/build`
   - 在 'Build Steps' 中点击 'Add Build Step'
   - 选择 'Custom Process Step'
   - Command: `./dev.sh`
   - Working directory: `%{sourceDir}`

**📋 步骤3: 添加生产构建配置**
1. 在 'Build Settings' 中点击 'Add' -> 'Clone Selected'
2. 重命名为 'Production'
3. 修改 Build directory 为: `%{sourceDir}/build-macos-prod`
4. 修改 Custom Process Step 的 Command 为: `./build.sh`
5. 修改 Arguments 为: `-p macos -e prod`

**📋 步骤4: 配置运行设置**
1. 切换到 'Run Settings'
2. 在 'Executable' 中设置: `%{sourceDir}/build/aiwuu-classroom`
3. 在 'Working directory' 中设置: `%{sourceDir}/build`

**🎯 完成后你就可以：**
- 在构建配置下拉菜单中选择 'Debug' 或 'Production'
- 点击构建按钮 (锤子图标) 执行对应的脚本
- 点击运行按钮 (绿色三角) 运行程序

**💡 提示：**
- 如果看不到构建配置选择，请确保已完成项目配置
- 构建配置选择器通常在工具栏的左侧
- 如果遇到问题，可以关闭项目重新打开

## 🎯 配置系统 (类似前端的env文件)

项目使用了完整的多环境配置系统，支持开发、测试、生产环境的配置分离，类似于前端项目的config/env文件管理方式。

### 📋 环境配置文件

#### 主配置文件 (`include/core/config.h`)

```cpp
// 环境类型定义
#define ENV_DEV 1
#define ENV_TEST 2
#define ENV_PROD 3

// 当前环境设置
#ifndef CURRENT_ENV
#define CURRENT_ENV ENV_DEV
#endif

// 根据当前环境选择配置文件
#if CURRENT_ENV == ENV_DEV
#include "config.dev.h"
#elif CURRENT_ENV == ENV_TEST
#include "config.test.h"
#elif CURRENT_ENV == ENV_PROD
#include "config.prod.h"
#endif
```

#### 开发环境配置 (`include/core/config.dev.h`)

```cpp
// 开发环境配置
#define APP_NAME "AIWUU Classroom Dev"
#define APP_VERSION "0.1.0-dev"
#define DEBUG_MODE true
#define LOG_LEVEL "debug"
#define API_BASE_URL "http://192.168.16.116:80"
#define DATABASE_PATH "./data/dev.db"

// 窗口配置
#define WINDOW_WIDTH 800
#define WINDOW_HEIGHT 600
#define WINDOW_TITLE "AIWUU Classroom - Development"

// 主题配置
#define PRIMARY_COLOR "#4A90E2"
#define SECONDARY_COLOR "#50E3C2"
#define BACKGROUND_COLOR "#F5F5F5"
#define TEXT_COLOR "#333333"
```

#### 生产环境配置 (`include/core/config.prod.h`)

```cpp
// 生产环境配置
#define APP_NAME "AIWUU Classroom"
#define APP_VERSION "1.0.0"
#define DEBUG_MODE false
#define LOG_LEVEL "info"
#define API_BASE_URL "http://192.168.16.116:80"
#define DATABASE_PATH "/var/lib/aiwuu/prod.db"

// 窗口配置
#define WINDOW_WIDTH 1024
#define WINDOW_HEIGHT 768
#define WINDOW_TITLE "AIWUU Classroom"

// 主题配置
#define PRIMARY_COLOR "#2C3E50"
#define SECONDARY_COLOR "#27AE60"
#define BACKGROUND_COLOR "#FFFFFF"
#define TEXT_COLOR "#2C3E50"
```

### 🔧 动态配置管理类

项目还提供了运行时配置管理类 (`include/utils/config.h`)：

```cpp
class Config : public QObject {
    Q_OBJECT

public:
    static Config& getInstance();

    // 应用程序配置
    struct AppConfig {
        QString appName;
        QString version;
        QString windowTitle;
        bool debugMode;
        QString logLevel;
    };

    // UI配置
    struct UIConfig {
        QString titleText;
        int titleFontSize;
        QString primaryColor;
        QString backgroundColor;
        int windowWidth;
        int windowHeight;
    };

    // API配置
    struct APIConfig {
        QString baseUrl;
        QString loginEndpoint;
        QString logoutEndpoint;
        int timeout;
        bool useHttps;
    };

    // 获取配置
    AppConfig getAppConfig() const;
    UIConfig getUIConfig() const;
    APIConfig getAPIConfig() const;

    // 通用配置方法
    QVariant getValue(const QString& key, const QVariant& defaultValue = QVariant()) const;
    void setValue(const QString& key, const QVariant& value);

    // 加载和保存配置
    void loadFromFile(const QString& filePath = "");
    void saveToFile(const QString& filePath = "") const;

    // 重置为默认配置
    void resetToDefaults();
};
```

### 📋 如何使用配置系统

#### 1. 在代码中使用环境配置

```cpp
MainWindow::MainWindow(QWidget *parent) : QMainWindow(parent) {
  // 使用配置文件中的宽高
  resize(WINDOW_WIDTH, WINDOW_HEIGHT);
  // 使用配置文件中的应用名称
  setWindowTitle(APP_NAME);
}
```

#### 2. 在main.cc中设置环境

```cpp
int main(int argc, char *argv[]) {
  QApplication app(argc, argv);

  // 设置环境
  NetworkManager::getInstance().setEnvironment("dev");

  // 设置应用程序信息
  QApplication::setApplicationName("Aiwuu Classroom");
  QApplication::setApplicationVersion("1.0.0");
}
```

#### 3. 存储和持久化配置

项目使用QSettings进行配置持久化：

```cpp
Storage::Storage(QObject* parent) : QObject(parent) {
    // 初始化 QSettings，使用应用程序名称作为组织名
    settings = new QSettings(QCoreApplication::organizationName(),
                           QCoreApplication::applicationName(),
                           this);
}

void Storage::setItem(const QString& key, const QVariant& value) {
    QString fullKey = STORAGE_PREFIX + key;
    settings->setValue(fullKey, value);
    settings->sync();
    emit storageChanged(key, value);
}
```

### 🔧 如何切换环境

#### 方法1: 编译时切换

在CMakeLists.txt中设置：

```cmake
# 设置环境
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    add_definitions(-DCURRENT_ENV=ENV_DEV)
elseif(CMAKE_BUILD_TYPE STREQUAL "Release")
    add_definitions(-DCURRENT_ENV=ENV_PROD)
endif()
```

#### 方法2: 使用项目脚本

```bash
# 切换到开发环境
./switch_ui_mode.sh ui

# 或者手动设置
cmake -DCURRENT_ENV=ENV_DEV ..
```

### 💡 配置系统的优势

1. **多环境支持**: 开发、测试、生产环境分离
2. **编译时优化**: 使用宏定义，编译时确定配置
3. **运行时配置**: 支持动态配置修改
4. **持久化存储**: 使用QSettings自动保存用户配置
5. **类型安全**: 使用结构体定义配置项

### 🎨 实际应用示例

如果你想修改登录窗口的标题，可以：

1. **修改配置文件**：
   ```cpp
   // 在config.dev.h中
   #define WINDOW_TITLE "新的标题"
   ```

2. **使用动态配置**：
   ```cpp
   Config::getInstance().setValue("ui/titleText", "新的标题");
   ```

3. **在代码中读取**：
   ```cpp
   QString title = Config::getInstance().getValue("ui/titleText", WINDOW_TITLE).toString();
   ```

这个配置系统非常类似于前端的环境配置，支持多环境、动态配置和持久化存储！

## 工具类使用示例

### 1. 网络请求工具 (NetworkManager)

#### 基础配置

```cc
// 设置基础 URL
NetworkManager::getInstance().setBaseUrl("http://api.example.com");

// 设置自定义请求头
NetworkManager::getInstance().setHeader("X-Custom-Header", "value");

// 设置最大重试次数
NetworkManager::getInstance().setMaxRetries(3);
```

#### 认证机制说明

NetworkManager 提供了类似 axios 的自动认证管理功能：

1. **自动 Token 管理**：登录成功后，token 会自动保存并在后续请求中使用
2. **认证控制**：通过 `requireAuth` 参数控制接口是否需要认证
3. **Authorization 头格式**：`Authorization: Bearer {token}`

#### 登录和 Token 管理

```cc
// 登录请求（不需要认证）
QJsonObject loginData;
loginData["username"] = "admin";
loginData["password"] = "123456";

NetworkManager::getInstance().post("auth/login", loginData,
    QMap<QString, QString>(), QMap<QString, QString>(),
    [](const QJsonObject& response) {
        // Token 会自动从响应的 data.token 字段提取并保存
        qDebug() << "登录成功，Token 已自动保存";
    },
    [](const QString& error) {
        qDebug() << "登录失败:" << error;
    },
    false  // 登录接口不需要认证
);

// 检查是否有 token
if (TokenManager::getInstance().hasToken()) {
    qDebug() << "已登录";
} else {
    qDebug() << "未登录";
}

// 检查 token 是否过期
if (TokenManager::getInstance().isTokenExpired()) {
    qDebug() << "Token 已过期";
} else {
    qDebug() << "Token 有效";
}

// 获取 token 过期时间
QDateTime expirationTime = TokenManager::getInstance().getExpirationTime();
qDebug() << "Token 过期时间:" << expirationTime.toString();

// 清除 token（登出时使用）
TokenManager::getInstance().clearToken();
```

#### 请求拦截器

```cc
// 创建请求拦截器
class AuthInterceptor : public NetworkManager::RequestInterceptor {
public:
    bool interceptRequest(QNetworkRequest& request) override {
        // 在请求发送前添加认证信息
        request.setRawHeader("X-API-Key", "your-api-key");
        return true;
    }
};

// 创建响应拦截器
class ErrorInterceptor : public NetworkManager::ResponseInterceptor {
public:
    bool interceptResponse(QNetworkReply* reply, QJsonObject& response) override {
        // 处理响应错误
        if (response.contains("error")) {
            qDebug() << "API 错误:" << response["error"].toString();
            return false;
        }
        return true;
    }
};

// 添加拦截器
NetworkManager::getInstance().addRequestInterceptor(new AuthInterceptor());
NetworkManager::getInstance().addResponseInterceptor(new ErrorInterceptor());
```

#### 认证控制

NetworkManager 支持类似 axios 的认证控制功能，可以根据接口需求自动管理 Authorization 头：

```cc
// 需要认证的接口（默认行为）
// 自动添加 Authorization: Bearer {token} 头
NetworkManager::getInstance().get("/user/info",
    QMap<QString, QString>(), QMap<QString, QString>(),
    [this](const QJsonObject& response) {
        // 处理成功响应
    },
    [this](const QString& error) {
        // 处理错误
    }
);

// 不需要认证的接口
// 不添加 Authorization 头
NetworkManager::getInstance().post("/auth/login", loginData,
    QMap<QString, QString>(), QMap<QString, QString>(),
    [this](const QJsonObject& response) {
        // 处理成功响应
    },
    [this](const QString& error) {
        // 处理错误
    },
    false  // requireAuth = false，不需要认证
);

// 其他不需要认证的接口示例
NetworkManager::getInstance().post("/auth/register", registerData, {}, {}, onSuccess, onError, false);
NetworkManager::getInstance().get("/public/config", {}, {}, onSuccess, onError, false);
NetworkManager::getInstance().post("/auth/forgot-password", data, {}, {}, onSuccess, onError, false);
```

#### 完整的认证流程示例

```cc
class ApiService {
public:
    // 1. 登录（不需要认证）
    void login(const QString& username, const QString& password) {
        QJsonObject loginData;
        loginData["username"] = username;
        loginData["password"] = password;
        
        NetworkManager::getInstance().post("/auth/classRoomLogin", loginData, {}, {},
            [this](const QJsonObject& response) {
                // Token 自动保存，可以直接调用需要认证的接口
                qDebug() << "登录成功";
                getUserInfo(); // 调用需要认证的接口
            },
            [](const QString& error) {
                qDebug() << "登录失败:" << error;
            },
            false  // 不需要认证
        );
    }
    
    // 2. 获取用户信息（需要认证）
    void getUserInfo() {
        NetworkManager::getInstance().get("/user/info", {}, {},
            [](const QJsonObject& response) {
                qDebug() << "用户信息:" << response;
            },
            [](const QString& error) {
                qDebug() << "获取用户信息失败:" << error;
            }
            // 默认 requireAuth = true，会自动添加 Authorization 头
        );
    }
    
    // 3. 更新用户信息（需要认证）
    void updateUserInfo(const QJsonObject& userInfo) {
        NetworkManager::getInstance().put("/user/update", userInfo, {}, {},
            [](const QJsonObject& response) {
                qDebug() << "更新成功";
            },
            [](const QString& error) {
                qDebug() << "更新失败:" << error;
            }
            // 默认需要认证
        );
    }
    
    // 4. 登出
    void logout() {
        NetworkManager::getInstance().post("/auth/logout", {}, {}, {},
            [](const QJsonObject& response) {
                // 清除本地 token
                TokenManager::getInstance().clearToken();
                qDebug() << "登出成功";
            },
            [](const QString& error) {
                qDebug() << "登出失败:" << error;
            }
            // 默认需要认证
        );
    }
    
    // 5. 注册（不需要认证）
    void registerUser(const QString& username, const QString& password, const QString& email) {
        QJsonObject registerData;
        registerData["username"] = username;
        registerData["password"] = password;
        registerData["email"] = email;
        
        NetworkManager::getInstance().post("/auth/register", registerData, {}, {},
            [](const QJsonObject& response) {
                qDebug() << "注册成功";
            },
            [](const QString& error) {
                qDebug() << "注册失败:" << error;
            },
            false  // 不需要认证
        );
    }
};
```

#### 基本请求

```cc
// GET 请求（默认需要认证）
NetworkManager::getInstance().get("users",
    [this](const QJsonObject& response) {
        // 处理成功响应
    },
    [this](const QString& error) {
        // 处理错误
    }
);

// POST 请求（默认需要认证）
QJsonObject data;
data["name"] = "新用户";
data["email"] = "<EMAIL>";

NetworkManager::getInstance().post("users", data,
    [this](const QJsonObject& response) {
        // 处理成功响应
    },
    [this](const QString& error) {
        // 处理错误
    }
);
```

#### 带查询参数的请求

```cc
// GET /api/users?page=1&limit=10
QMap<QString, QString> queryParams;
queryParams["page"] = "1";
queryParams["limit"] = "10";

NetworkManager::getInstance().get("users", queryParams);
```

#### 带路径参数的请求

```cc
// GET /api/users/{id}/posts/{postId}
QMap<QString, QString> pathParams;
pathParams["id"] = "123";
pathParams["postId"] = "456";

NetworkManager::getInstance().get("users/{id}/posts/{postId}",
    QMap<QString, QString>(), pathParams);
```

#### 组合使用

```cc
// GET /api/users/{id}/posts?page=1&limit=10
QMap<QString, QString> queryParams;
queryParams["page"] = "1";
queryParams["limit"] = "10";

QMap<QString, QString> pathParams;
pathParams["id"] = "123";

NetworkManager::getInstance().get("users/{id}/posts",
    queryParams, pathParams);
```

### 2. 消息提示工具 (MessageBox)

```cc
// 成功提示
MessageBox::success(this, "操作成功", "数据已保存");

// 错误提示
MessageBox::error(this, "操作失败", "请检查网络连接");

// 警告提示
MessageBox::warning(this, "警告", "此操作不可撤销");

// 信息提示
MessageBox::info(this, "提示", "新消息已到达");

// 确认对话框
if (MessageBox::confirm(this, "确认", "确定要删除吗？")) {
    // 用户点击了确定
} else {
    // 用户点击了取消
}
```

### 3. 表单工具 (FormUtils)

#### 基础验证

```cc
// 必填验证
if (!FormUtils::validateRequired(username, "用户名")) {
    return;
}

// 长度验证
if (!FormUtils::validateLength(password, 6, 20, "密码")) {
    return;
}

// 邮箱验证
if (!FormUtils::validateEmail(email)) {
    return;
}

// 手机号验证
if (!FormUtils::validatePhone(phone)) {
    return;
}
```

#### 自定义验证

```cc
// 单个自定义验证器
auto isPositive = [](const QString& value) {
    return value.toInt() > 0;
};

if (!FormUtils::validateCustom(age, "年龄", isPositive, "年龄必须大于0")) {
    return;
}

// 多个验证规则
QList<std::function<bool(const QString&)>> passwordRules = {
    [](const QString& pwd) { return pwd.length() >= 8; },
    [](const QString& pwd) { return pwd.contains(QRegExp("[A-Z]")); },
    [](const QString& pwd) { return pwd.contains(QRegExp("[0-9]")); }
};

QList<QString> passwordErrors = {
    "密码长度至少8位",
    "密码必须包含大写字母",
    "密码必须包含数字"
};

if (!FormUtils::validateWithRules(password, "密码", passwordRules, passwordErrors)) {
    return;
}
```

#### 表单验证

```cc
// 定义表单数据
QMap<QString, QString> formData = {
    {"username", username},
    {"password", password},
    {"email", email},
    {"phone", phone}
};

// 定义验证规则
QMap<QString, QList<std::function<bool(const QString&)>>> rules = {
    {"username", {
        [](const QString& val) { return !val.isEmpty(); },
        [](const QString& val) { return val.length() >= 3; }
    }},
    {"password", {
        [](const QString& val) { return val.length() >= 8; },
        [](const QString& val) { return val.contains(QRegExp("[A-Z]")); }
    }},
    {"email", {
        [](const QString& val) { return val.contains("@"); }
    }},
    {"phone", {
        [](const QString& val) { return val.length() == 11; }
    }}
};

// 定义错误消息
QMap<QString, QList<QString>> errorMessages = {
    {"username", {"用户名不能为空", "用户名至少3个字符"}},
    {"password", {"密码至少8位", "密码必须包含大写字母"}},
    {"email", {"邮箱格式不正确"}},
    {"phone", {"手机号必须是11位"}}
};

// 验证表单
if (!FormUtils::validateForm(formData, rules, errorMessages)) {
    return;
}
```

## API 接口调用示例

### 用户相关接口

#### 登录

```cc
// 登录请求
QNetworkReply* reply = Api::login("username", "password");
connect(reply, &QNetworkReply::finished, [=]() {
    if (reply->error() == QNetworkReply::NoError) {
        QJsonObject response = QJsonDocument::fromJson(reply->readAll()).object();
        // 处理登录成功响应
        QString token = response["token"].toString();
        // 存储token
        Storage::getInstance().setItem("token", token);
    } else {
        // 处理登录失败
        MessageBox::error("登录失败", reply->errorString());
    }
    reply->deleteLater();
});
```

#### 注册

```cc
// 注册请求
QNetworkReply* reply = Api::registerUser("username", "password", "<EMAIL>");
connect(reply, &QNetworkReply::finished, [=]() {
    if (reply->error() == QNetworkReply::NoError) {
        MessageBox::success("注册成功", "请登录");
    } else {
        MessageBox::error("注册失败", reply->errorString());
    }
    reply->deleteLater();
});
```

#### 获取用户信息

```cc
// 获取用户信息
QNetworkReply* reply = Api::getUserInfo();
connect(reply, &QNetworkReply::finished, [=]() {
    if (reply->error() == QNetworkReply::NoError) {
        QJsonObject userInfo = QJsonDocument::fromJson(reply->readAll()).object();
        // 更新UI显示用户信息
        updateUserInfoDisplay(userInfo);
    } else {
        MessageBox::error("获取用户信息失败", reply->errorString());
    }
    reply->deleteLater();
});
```

### 课程相关接口

#### 获取课程列表

```cc
// 获取课程列表（分页）
QNetworkReply* reply = Api::getCourseList(1, 10); // 第1页，每页10条
connect(reply, &QNetworkReply::finished, [=]() {
    if (reply->error() == QNetworkReply::NoError) {
        QJsonObject response = QJsonDocument::fromJson(reply->readAll()).object();
        QJsonArray courses = response["data"].toArray();
        // 更新课程列表UI
        updateCourseList(courses);
    } else {
        MessageBox::error("获取课程列表失败", reply->errorString());
    }
    reply->deleteLater();
});
```

#### 获取课程详情

```cc
// 获取课程详情
int courseId = 123;
QNetworkReply* reply = Api::getCourseDetail(courseId);
connect(reply, &QNetworkReply::finished, [=]() {
    if (reply->error() == QNetworkReply::NoError) {
        QJsonObject courseDetail = QJsonDocument::fromJson(reply->readAll()).object();
        // 显示课程详情
        showCourseDetail(courseDetail);
    } else {
        MessageBox::error("获取课程详情失败", reply->errorString());
    }
    reply->deleteLater();
});
```

#### 创建课程

```cc
// 创建新课程
QJsonObject courseInfo;
courseInfo["name"] = "Qt编程基础";
courseInfo["description"] = "Qt基础入门课程";
courseInfo["category"] = "编程";
courseInfo["price"] = 99.9;

QNetworkReply* reply = Api::createCourse(courseInfo);
connect(reply, &QNetworkReply::finished, [=]() {
    if (reply->error() == QNetworkReply::NoError) {
        MessageBox::success("创建成功", "课程已创建");
        // 刷新课程列表
        refreshCourseList();
    } else {
        MessageBox::error("创建课程失败", reply->errorString());
    }
    reply->deleteLater();
});
```

#### 更新课程

```cc
// 更新课程信息
int courseId = 123;
QJsonObject courseInfo;
courseInfo["name"] = "Qt编程进阶";
courseInfo["description"] = "Qt高级编程课程";
courseInfo["price"] = 199.9;

QNetworkReply* reply = Api::updateCourse(courseId, courseInfo);
connect(reply, &QNetworkReply::finished, [=]() {
    if (reply->error() == QNetworkReply::NoError) {
        MessageBox::success("更新成功", "课程信息已更新");
        // 刷新课程详情
        refreshCourseDetail(courseId);
    } else {
        MessageBox::error("更新课程失败", reply->errorString());
    }
    reply->deleteLater();
});
```

#### 删除课程

```cc
// 删除课程
int courseId = 123;
QNetworkReply* reply = Api::deleteCourse(courseId);
connect(reply, &QNetworkReply::finished, [=]() {
    if (reply->error() == QNetworkReply::NoError) {
        MessageBox::success("删除成功", "课程已删除");
        // 刷新课程列表
        refreshCourseList();
    } else {
        MessageBox::error("删除课程失败", reply->errorString());
    }
    reply->deleteLater();
});
```

### 注意事项

1. 所有网络请求都应该处理错误情况
2. 使用 `reply->deleteLater()` 确保正确释放资源
3. 在请求完成后更新 UI
4. 使用 `MessageBox` 显示操作结果
5. 适当使用 `Storage` 存储必要的数据（如 token）

## 构建过程

1. 清理旧的构建文件
2. 根据环境生成 Makefile
3. 编译项目
4. 运行程序

## 常见问题

1. 如果遇到权限问题，请确保脚本有执行权限：

```bash
chmod +x dev.sh
```

2. 如果需要清理项目：

```bash
make clean
```

## Qt 生命周期与事件系统

Qt 提供了类似前端框架的生命周期概念，主要通过事件系统（Event System）和信号槽机制（Signals & Slots）来实现。

### 生命周期事件

#### 1. 初始化阶段

```cc
// 窗口显示事件，相当于 Vue 的 onMounted
void MainWindow::showEvent(QShowEvent *event)
{
    // 调用父类的 showEvent
    QMainWindow::showEvent(event);

    // 窗口显示时加载数据
    qDebug() << "MainWindow shown, loading data...";
    loadCourseList();
}
```

#### 2. 销毁阶段

```cc
// 窗口关闭事件，相当于 Vue 的 onUnmounted
void MainWindow::closeEvent(QCloseEvent *event)
{
    // 窗口关闭时的清理工作
    qDebug() << "MainWindow closing, cleaning up...";

    // 调用父类的 closeEvent
    QMainWindow::closeEvent(event);
}
```

### 常用事件列表

1. **showEvent**: 窗口显示时触发

   - 适合进行初始化操作
   - 相当于 Vue 的 `onMounted`

2. **closeEvent**: 窗口关闭时触发

   - 适合进行清理工作
   - 相当于 Vue 的 `onUnmounted`

3. **paintEvent**: 窗口需要重绘时触发

   - 适合进行自定义绘制
   - 相当于 Vue 的 `onUpdated`

4. **resizeEvent**: 窗口大小改变时触发

   - 适合调整布局
   - 相当于 Vue 的 `onResize`

5. **keyPressEvent**: 键盘按键按下时触发

   - 适合处理键盘输入
   - 相当于 Vue 的 `onKeyDown`

6. **mousePressEvent**: 鼠标点击时触发
   - 适合处理鼠标交互
   - 相当于 Vue 的 `onClick`

### 与 Vue 的对比

1. **实现方式**

   - Vue: 使用生命周期钩子函数
   - Qt: 使用事件系统

2. **灵活性**

   - Vue: 钩子函数固定，但使用简单
   - Qt: 事件系统更底层，可以自定义事件

3. **扩展性**
   - Vue: 通过组合式 API 扩展
   - Qt: 通过继承和重写事件处理函数扩展

### 使用示例

```cc
class MainWindow : public QMainWindow
{
    Q_OBJECT

protected:
    // 窗口显示事件
    void showEvent(QShowEvent *event) override {
        QMainWindow::showEvent(event);
        // 初始化操作
        loadData();
    }

    // 窗口关闭事件
    void closeEvent(QCloseEvent *event) override {
        // 清理操作
        cleanup();
        QMainWindow::closeEvent(event);
    }

private:
    void loadData() {
        // 加载数据
        QNetworkReply* reply = Api::getCourseList(1, 10);
        connect(reply, &QNetworkReply::finished, [=]() {
            if (reply->error() == QNetworkReply::NoError) {
                // 处理数据
                QJsonObject response = QJsonDocument::fromJson(reply->readAll()).object();
                updateUI(response);
            }
            reply->deleteLater();
        });
    }

    void cleanup() {
        // 清理资源
        // 例如：关闭数据库连接、保存设置等
    }
};
```

### 注意事项

1. 事件处理函数必须调用父类的对应函数
2. 使用 `override` 关键字标记重写的函数
3. 在事件处理函数中避免耗时操作
4. 合理使用信号槽机制处理异步操作
5. 注意资源的正确释放

## 打包配置说明

### 支持的平台

目前项目支持以下平台的打包：

- Windows (.exe)
- Linux (AppImage)

### 打包命令

使用 `build.sh` 脚本进行打包：

```bash
# Windows 版本
./build.sh -p windows -e prod

# Linux 版本
./build.sh -p linux -e prod
```

### 打包选项

- `-p, --platform`: 指定目标平台
  - `windows`: Windows 平台
  - `linux`: Linux 平台
- `-e, --env`: 指定构建环境
  - `dev`: 开发环境
  - `prod`: 生产环境
- `-h, --help`: 显示帮助信息

### 打包输出

#### Windows

- 可执行文件: `aiwuu-classroom.exe`
- 依赖库文件
- 资源文件

#### Linux

- 可执行文件: `aiwuu-classroom`
- AppImage 文件: `aiwuu-classroom.AppImage`

### 平台特定配置

#### Windows 配置

```qmake
win32 {
    # Windows 特定配置
    RC_ICONS = resources/icon.ico
    QMAKE_TARGET_COMPANY = "AIWUU"
    QMAKE_TARGET_PRODUCT = "AIWUU Classroom"
    QMAKE_TARGET_DESCRIPTION = "AIWUU Classroom Application"
    QMAKE_TARGET_COPYRIGHT = "Copyright (C) 2024"
}
```

#### Linux 配置

```qmake
linux {
    # Linux 特定配置
    desktop.path = /usr/share/applications
    desktop.files = resources/aiwuu-classroom.desktop
    INSTALLS += desktop
}
```

### 打包工具要求

#### Windows

- MinGW 或 MSVC 编译器
- Qt 5.10 或更高版本
- windeployqt 工具
- NSIS 或 Inno Setup（用于创建安装包）

#### Linux

- GCC 编译器
- Qt 5.10 或更高版本
- linuxdeployqt 工具

### 打包过程

1. **清理旧的构建文件**

   ```bash
   make clean
   ```

2. **创建构建目录**

   ```bash
   mkdir -p build
   cd build
   ```

3. **生成 Makefile**

   ```bash
   # Windows
   qmake ../aiwuu.pro -spec win32-g++ CONFIG+=release

   # Linux
   qmake ../aiwuu.pro -spec linux-g++ CONFIG+=release
   ```

4. **编译项目**

   ```bash
   make -j$(nproc)
   ```

5. **打包应用程序**
   - Windows: 使用 windeployqt 复制依赖
   - Linux: 使用 linuxdeployqt 创建 AppImage

### 注意事项

1. **资源文件**

   - 确保所有资源文件都在 `resources` 目录下
   - Windows 需要 `.ico` 图标文件
   - Linux 需要 `.desktop` 文件

2. **依赖处理**

   - Windows: 使用 `windeployqt` 自动处理依赖
   - Linux: 使用 `linuxdeployqt` 自动处理依赖

3. **版本控制**

   - 在项目文件中设置版本号
   - 在应用程序中显示版本信息

4. **环境配置**
   - 开发环境: 使用 `config.dev.h`
   - 生产环境: 使用 `config.prod.h`

### 常见问题

1. **找不到打包工具**

   ```bash
   # 检查工具是否安装
   which windeployqt  # Windows
   which linuxdeployqt  # Linux
   ```

2. **依赖问题**

   - 确保所有 Qt 模块都已正确安装
   - 检查是否缺少必要的系统库

3. **打包失败**
   - 检查构建日志
   - 确保有足够的磁盘空间
   - 验证所有资源文件是否存在

### 扩展打包配置

如果需要添加新的打包配置，可以：

1. 在 `aiwuu.pro` 中添加平台特定配置
2. 在 `build.sh` 中添加相应的打包步骤
3. 更新帮助信息和平台验证

### 参考配置

完整的打包配置可以参考：

- `aiwuu.pro`: 项目配置文件
- `build.sh`: 打包脚本
- `resources/`: 资源文件目录

## 调试指南

### 1. 调试工具

#### Qt Creator 调试器

- 内置断点调试
- 变量监视
- 调用栈查看
- 内存检查

#### 控制台输出

```cc
// 基本调试输出
qDebug() << "调试信息";
qWarning() << "警告信息";
qCritical() << "错误信息";
qFatal() << "致命错误";  // 会导致程序终止

// 格式化输出
qDebug() << QString("用户: %1, 年龄: %2").arg(username).arg(age);

// 条件调试
Q_ASSERT(condition);  // 断言
Q_ASSERT_X(condition, "context", "message");  // 带上下文的断言
```

### 2. 样式调试

#### Qt Style Sheets (QSS)

```cc
// 在代码中设置样式
widget->setStyleSheet("QPushButton { color: red; }");

// 加载外部样式文件
QFile file(":/styles/main.qss");
file.open(QFile::ReadOnly);
QString styleSheet = QLatin1String(file.readAll());
qApp->setStyleSheet(styleSheet);
```

#### 样式调试工具

1. **Qt Style Sheet Editor**

   - 实时预览样式效果
   - 支持语法高亮
   - 自动补全

2. **Qt Designer**
   - 可视化编辑界面
   - 实时预览样式
   - 支持样式继承

### 3. 网络调试

#### 网络请求调试

```cc
// 启用网络调试
QLoggingCategory::setFilterRules("qt.network.*=true");

// 在 NetworkManager 中添加调试信息
void NetworkManager::handleReply(QNetworkReply* reply) {
    qDebug() << "请求URL:" << reply->request().url().toString();
    qDebug() << "请求方法:" << reply->request().method();
    qDebug() << "请求头:" << reply->request().rawHeaderList();

    if (reply->error() == QNetworkReply::NoError) {
        qDebug() << "响应数据:" << reply->readAll();
    } else {
        qDebug() << "错误:" << reply->errorString();
    }
}
```

#### 使用 Charles/Fiddler

- 抓包分析
- 请求/响应查看
- 网络性能分析

### 4. 性能调试

#### 性能分析工具

1. **Qt Creator Profiler**

   - CPU 使用分析
   - 内存使用分析
   - 函数调用分析

2. **Valgrind (Linux)**
   - 内存泄漏检测
   - 线程错误检测
   - 性能分析

#### 性能调试代码

```cc
// 计时器
QElapsedTimer timer;
timer.start();
// ... 执行代码 ...
qDebug() << "耗时:" << timer.elapsed() << "ms";

// 内存使用
qDebug() << "内存使用:" << QProcess::systemMemoryUsage();
```

### 5. 调试技巧

#### 条件编译

```cc
#ifdef QT_DEBUG
    // 调试代码
    qDebug() << "调试信息";
#endif
```

#### 日志文件

```cc
// 配置日志文件
void setupLogging() {
    QFile logFile("debug.log");
    logFile.open(QIODevice::WriteOnly | QIODevice::Append);
    QTextStream logStream(&logFile);

    // 重定向调试输出到文件
    qInstallMessageHandler([](QtMsgType type, const QMessageLogContext &context, const QString &msg) {
        QFile logFile("debug.log");
        logFile.open(QIODevice::WriteOnly | QIODevice::Append);
        QTextStream logStream(&logFile);
        logStream << QDateTime::currentDateTime().toString() << " - " << msg << "\n";
    });
}
```

#### 调试宏

```cc
#define DEBUG_LOG(msg) qDebug() << Q_FUNC_INFO << ":" << msg
#define DEBUG_ERROR(msg) qCritical() << Q_FUNC_INFO << ":" << msg
```

### 6. 常见调试场景

#### UI 调试

1. **布局问题**

   ```cc
   // 显示布局边框
   layout->setContentsMargins(1, 1, 1, 1);
   layout->setSpacing(1);

   // 打印布局信息
   qDebug() << "布局大小:" << layout->sizeHint();
   qDebug() << "布局边距:" << layout->contentsMargins();
   ```

2. **样式问题**
   ```cc
   // 打印样式信息
   qDebug() << "当前样式:" << widget->styleSheet();
   qDebug() << "继承样式:" << widget->style()->objectName();
   ```

#### 数据调试

1. **JSON 数据**

   ```cc
   // 格式化输出 JSON
   QJsonDocument doc = QJsonDocument::fromJson(data);
   qDebug() << "JSON 数据:" << doc.toJson(QJsonDocument::Indented);
   ```

2. **数据库查询**
   ```cc
   // 打印 SQL 查询
   qDebug() << "SQL 查询:" << query.lastQuery();
   qDebug() << "查询结果:" << query.lastError().text();
   ```

### 7. 调试工具配置

#### VSCode 配置

```json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Qt Debug",
      "type": "cppdbg",
      "request": "launch",
      "program": "${workspaceFolder}/build/aiwuu-classroom",
      "args": [],
      "stopAtEntry": false,
      "cwd": "${workspaceFolder}",
      "environment": [],
      "externalConsole": false,
      "MIMode": "gdb",
      "setupCommands": [
        {
          "description": "Enable pretty-printing for gdb",
          "text": "-enable-pretty-printing",
          "ignoreFailures": true
        }
      ]
    }
  ]
}
```

#### Qt Creator 配置

1. 打开项目
2. 选择 Debug 模式
3. 设置断点
4. 按 F5 开始调试

### 8. 调试最佳实践

1. **使用版本控制**

   - 在调试前提交代码
   - 创建调试分支

2. **记录调试过程**

   - 记录问题现象
   - 记录解决步骤
   - 记录相关代码

3. **代码审查**

   - 检查内存管理
   - 检查资源释放
   - 检查异常处理

4. **性能优化**
   - 使用性能分析工具
   - 优化关键路径
   - 减少不必要的操作

## QSS 样式指南

### 1. 基本概念

QSS（Qt Style Sheets）是 Qt 的样式表系统，类似于 CSS，用于自定义 Qt 应用程序的外观。

#### 选择器类型

- ID 选择器（`#`）：
  ```qss
  #loginContainer {
      background-color: white;
  }
  ```

- 类选择器（`.`）：
  ```qss
  .QPushButton {
      border: none;
  }
  ```

- 类型选择器：
  ```qss
  QLineEdit {
      padding: 12px;
  }
  ```

- 子控件选择器（`>`）：
  ```qss
  QWidget > QPushButton {
      margin: 10px;
  }
  ```

### 2. 使用 QSS 文件

#### 创建 QSS 文件

在 `resources/styles/` 目录下创建 `.qss` 文件，例如 `login.qss`：

```qss
#loginContainer {
    background-color: white;
    border-radius: 8px;
}

#loginContainer QLineEdit {
    padding: 12px;
    border: 1px solid #ddd;
}
```

#### 加载 QSS 文件

方法一：使用 `QFile` 加载
```cpp
void LoginWindow::loadStyleSheet()
{
    QFile file(":/styles/login.qss");
    if (file.open(QFile::ReadOnly | QFile::Text))
    {
        QString styleSheet = QLatin1String(file.readAll());
        qApp->setStyleSheet(styleSheet);  // 应用到整个应用程序
        // 或者
        this->setStyleSheet(styleSheet);   // 只应用到当前窗口
        file.close();
    }
}
```

方法二：直接在代码中设置
```cpp
void LoginWindow::loadStyleSheet()
{
    this->setStyleSheet(R"(
        #loginContainer {
            background-color: white;
        }
    )");
}
```

方法三：使用资源系统加载
```cpp
void LoginWindow::loadStyleSheet()
{
    this->setStyleSheet(":/styles/login.qss");
}
```

### 3. 样式应用范围

- 全局应用：
```cpp
// 在 main.cc 中
QApplication app(argc, argv);
app.setStyleSheet(":/styles/login.qss");
```

- 窗口级应用：
```cpp
// 在窗口构造函数中
this->setStyleSheet(":/styles/login.qss");
```

- 控件级应用：
```cpp
// 只对特定控件应用样式
QPushButton *button = new QPushButton(this);
button->setStyleSheet("background-color: blue;");
```

### 4. 动态修改样式

```cpp
// 根据状态改变样式
void LoginWindow::onLoginFailed()
{
    loginButton->setStyleSheet("background-color: red;");
}

// 使用属性选择器
void LoginWindow::onLoginFailed()
{
    loginButton->setProperty("state", "error");
    // 在 QSS 中定义
    // QPushButton[state="error"] { background-color: red; }
}
```

### 5. 样式继承

```qss
/* 父控件样式 */
#loginContainer {
    background-color: white;
}

/* 子控件继承父控件样式 */
#loginContainer QLineEdit {
    border: 1px solid #ddd;
}
```

### 6. 使用变量（Qt 5.12+）

```qss
/* 定义变量 */
:root {
    --primary-color: #1890ff;
    --error-color: #ff4d4f;
}

/* 使用变量 */
QPushButton {
    background-color: var(--primary-color);
}

QPushButton[state="error"] {
    background-color: var(--error-color);
}
```

### 7. 调试技巧

```cpp
// 打印当前样式
qDebug() << this->styleSheet();

// 检查样式是否加载成功
if (this->styleSheet().isEmpty()) {
    qDebug() << "样式表加载失败";
}
```

### 8. 最佳实践

- 将样式按功能模块分类
- 使用有意义的命名
- 使用注释说明样式用途
- 避免样式冲突
- 使用资源系统管理样式文件
- 考虑样式的可维护性和可重用性

## 构建说明

### 环境要求

- CMake 3.8 或更高版本
- Qt 5.12 或更高版本
- C++17 或更高版本

### 构建步骤

1. 创建构建目录：
```bash
mkdir build && cd build
```

2. 配置项目：
```bash
cmake ..
```

3. 构建项目：
```bash
make
```

## 贡献指南

1. Fork 本仓库
2. 创建您的特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交您的更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开一个 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## Aiwuu图像采集流程分析

通过分析代码，整个采集流程如下：

### 1. 开始采集

- 在Windows版本中，当用户点击"采集全部"按钮时，会调用`fxmy_capture_start()`函数
```cpp
// Windows平台
if (LOWORD(wParam) == ID_BTN_CAPTURE_ALL) {
  SetEnable(ID_BTN_CAPTURE_ALL, g_static_window, false);
  auto ret = fxmy_capture_start();
  if (ret != STATUS_OK) {
    MessageBox(hwnd, L"采集失败！", L"提示", MB_OK);
  }
}
```

- 在Qt版本中，也是类似的流程
```cpp
// Qt平台
QObject::connect(btn_capture_all, &QPushButton::clicked, [&]() {
  btn_capture_all->setEnabled(false);
  auto ret = fxmy_capture_start();
  if (ret != STATUS_OK) {
    QMessageBox::information(&window, "提示", "采集失败！");
  }
  btn_capture_all->setEnabled(true);
});
```

### 2. 采集过程

- 采集过程由fxmy库内部处理
- 当捕获状态变为`STATE_RUNNING`时，会通过`OnFxmyStateChanged`回调通知界面
```cpp
case STATE_RUNNING: {
  RunOnUIThread([] {
    SetStatusText(L"正在运行中...");
    // 隐藏状态窗口
    ShowWindow(g_status_window, SW_HIDE);
  });
  break;
}
```

### 3. 任务ID生成

- 每个采集任务都需要一个唯一ID，通过`OnGenTaskID`回调生成
- 在Windows上使用GUID，在Qt上使用QUuid
```cpp
void OnGenTaskID(ItemID item_id, CaptureID capture_id, TaskID *task_id, void *user_data) {
#if USE_QT
  // 通过Qt的UUID机制生成任务ID
  QUuid uuid = QUuid::createUuid();
  // 移除"{}", "-"等特殊字符
  QString gened_uuid = uuid.toString().remove(QRegExp("[{}-]"));
  // 转小写
  gened_uuid = gened_uuid.toLower();
  auto temp = gened_uuid.toStdString();
#else
  // 通过Windows的GUID机制生成任务ID
  GUID guid;
  HRESULT hr = CoCreateGuid(&guid);
  // 转换为字符串
  char guidString[128] = {0};
  snprintf(guidString, sizeof(guidString), "%08lx%04x%04x%04x%012llx", ...);
  std::string temp = guidString;
#endif
  memcpy(task_id->id, temp.c_str(), temp.size());
}
```

### 4. 设备发现

- 系统有一个设备枚举回调`OnDeviceFound`来处理发现的设备
```cpp
void OnDeviceFound(Device devices[], int count, void* user_data) {
  // 设备处理逻辑
}
```

### 5. 停止采集

- 在Windows版本中，当点击"取消采集"按钮时，调用`fxmy_capture_stop()`
- 在Qt版本中也有类似操作

### 关于图片保存

从代码中看不到直接的图片保存逻辑，这表明图片存储可能是在`fxmy`库内部处理的。以下是可能的原因：

1. 图片数据可能在`fxmy_capture_start()`函数内部自动处理
2. 可能存在未在此代码中显示的其他回调函数处理图片保存
3. 任务ID的生成表明每个采集任务有唯一标识，图片可能以此ID命名并保存

**总结**：整个采集流程是从UI触发`fxmy_capture_start()`开始，然后由fxmy库内部处理采集和存储，最后可通过`fxmy_capture_stop()`停止。图片保存细节不在此界面代码中，而是封装在fxmy库内部。
