#!/bin/bash

# Qt Creator UI设计器修复脚本
# 解决设计按钮不能点击和autogen目录显示问题

echo "🔧 修复Qt Creator UI设计器问题..."

# 1. 清理旧的构建文件
echo "📁 清理构建缓存..."
if [ -d "build" ]; then
    rm -rf build/aiwuu-classroom_autogen
    rm -rf build/.qt_autogen
    rm -f build/CMakeCache.txt
fi

# 2. 检查UI文件是否存在
echo "📋 检查UI文件..."
if [ ! -f "ui/loginwindow.ui" ]; then
    echo "❌ 错误：ui/loginwindow.ui 文件不存在"
    echo "请先切换到UI文件模式：./switch_ui_mode.sh ui"
    exit 1
fi

# 3. 确保当前是UI文件模式
echo "🔄 确保使用UI文件模式..."
./switch_ui_mode.sh ui

# 4. 重新配置CMake
echo "⚙️ 重新配置CMake..."
cd build
cmake .. -DUSE_UI_FILES=ON

# 5. 生成一次以创建ui_*.h文件
echo "🔨 生成UI头文件..."
make -j4

echo ""
echo "✅ 修复完成！"
echo ""
echo "📖 Qt Creator使用指南："
echo "1. 关闭Qt Creator中的当前项目"
echo "2. 重新打开项目（File -> Open File or Project -> CMakeLists.txt）"
echo "3. 在项目树中双击 ui/loginwindow.ui 文件"
echo "4. 现在设计按钮应该可以点击了"
echo ""
echo "💡 关于 autogen 目录："
echo "- 这是Qt自动生成的代码目录，包含MOC、UIC、RCC文件"
echo "- 这是正常的Qt开发流程，包含重要的生成文件（如ui_*.h）"
echo "- VSCode不显示此目录，但Qt Creator会在项目树中显示"
echo "- 可以在Qt Creator项目设置中隐藏此目录的显示"
echo ""
echo "🎯 如果设计按钮仍然不能点击："
echo "1. 确保Qt Creator版本支持Qt5.10+"
echo "2. 检查Qt Designer插件是否已启用"
echo "3. 尝试右键点击.ui文件 -> 'Open With' -> 'Qt Designer'"
echo ""
echo "📁 如何在Qt Creator中隐藏autogen目录："
echo "1. 在Qt Creator中右键点击项目根目录"
echo "2. 选择 'Hide Files Matching...' 或 '隐藏匹配的文件...'"
echo "3. 输入: *_autogen"
echo "4. 点击确定，autogen目录将不再显示" 