#ifndef CONFIG_H
#define CONFIG_H

// 环境类型定义
#define ENV_DEV 1
#define ENV_TEST 2
#define ENV_PROD 3

// 当前环境设置
#ifndef CURRENT_ENV
#define CURRENT_ENV ENV_DEV
#endif

// 根据当前环境选择配置文件
#if CURRENT_ENV == ENV_DEV
#include "config.dev.h"
#elif CURRENT_ENV == ENV_TEST
#include "config.test.h"
#elif CURRENT_ENV == ENV_PROD
#include "config.prod.h"
#else
#error "Unknown environment type"
#endif

#endif // CONFIG_H