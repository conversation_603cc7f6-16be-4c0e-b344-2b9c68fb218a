#ifndef LOGINWINDOW_UI_H
#define LOGINWINDOW_UI_H

#include "api/api.h"
#include "utils/formutils.h"
#include "utils/storage.h"
#include "utils/toast.h"
#include <QEvent>
#include <QLabel>
#include <QLineEdit>
#include <QObject>
#include <QPaintEvent>
#include <QPixmap>
#include <QPoint>
#include <QPushButton>
#include <QResizeEvent>
#include <QWidget>

QT_BEGIN_NAMESPACE
namespace Ui {
class LoginWindow;
}
QT_END_NAMESPACE

class LoginWindowUI : public QWidget {
  Q_OBJECT

public:
  explicit LoginWindowUI(QWidget *parent = nullptr);
  ~LoginWindowUI();

protected:
  void paintEvent(QPaintEvent *event) override;
  bool eventFilter(QObject *watched, QEvent *event) override;
  void resizeEvent(QResizeEvent *event) override;

private slots:
  void onLoginClicked();
  void onForgotPasswordClicked();
  void onLoginSuccess();
  void onLoginFailed(const QString &error);
  void showMainWindow();

private:
  void setupUI();
  void connectSignals();
  void loadStyleSheet();
  void updateImageScale(); // 更新图片缩放

  Ui::LoginWindow *ui;
  QPoint m_dragPosition;
  QPixmap m_bgImage; // 背景图片
};

#endif // LOGINWINDOW_UI_H