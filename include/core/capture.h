#ifndef CAPTURE_H
#define CAPTURE_H

#include <QHBoxLayout>
#include <QLabel>
#include <QPushButton>
#include <QTextEdit>
#include <QVBoxLayout>
#include <QWidget>

class Capture : public QWidget {
  Q_OBJECT

public:
  explicit Capture(QWidget *parent = nullptr);
  ~Capture();

private slots:
  void onStartCollectionClicked();
  void onStopCollectionClicked();
  void onBackClicked();
  void initializeFxmy();

signals:
  void backToMainWindow();

private:
  void setupUI();
  void setupConnections();

  // UI组件
  QVBoxLayout *mainLayout;
  QPushButton *backButton;
  QPushButton *startButton;
  QPushButton *stopButton;
  QTextEdit *logTextEdit;
};

#endif // CAPTURE_H