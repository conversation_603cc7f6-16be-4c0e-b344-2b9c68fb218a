#ifndef CAPTURE_UI_H
#define CAPTURE_UI_H

#include <QTimer>
#include <QWidget>

QT_BEGIN_NAMESPACE
namespace Ui {
class Capture;
}
QT_END_NAMESPACE

class CaptureUI : public QWidget {
  Q_OBJECT

public:
  explicit CaptureUI(QWidget *parent = nullptr);
  ~CaptureUI();

private slots:
  void onStartCollectionClicked();
  void onStopCollectionClicked();
  void onBackClicked();
  void updateProgress();

private:
  void setupUI();
  void setupConnections();
  void startCollection();
  void stopCollection();

  Ui::Capture *ui;

  // 状态管理
  QTimer *progressTimer;
  bool isCollecting;
  int progressValue;

signals:
  void backToMainWindow();
};

#endif // CAPTURE_UI_H