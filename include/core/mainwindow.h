#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include "api/api.h"
#include "app_config.h"
#include "utils/storage.h"
#include "utils/toast.h"
#include <QEvent>
#include <QHBoxLayout>
#include <QLabel>
#include <QLineEdit>
#include <QMainWindow>
#include <QNetworkReply>
#include <QPropertyAnimation>
#include <QPushButton>
#include <QStackedWidget>
#include <QTimer>
#include <QVBoxLayout>
#include <QWidget>

class MainWindow : public QMainWindow {
  Q_OBJECT

public:
  MainWindow(QWidget *parent = nullptr);
  ~MainWindow();

protected:
  // 窗口显示事件，相当于 Vue 的 onMounted
  void showEvent(QShowEvent *event) override;
  // 窗口关闭事件，相当于 Vue 的 onUnmounted
  void closeEvent(QCloseEvent *event) override;
  // 绘制事件，用于自定义背景
  void paintEvent(QPaintEvent *event) override;
  // 事件过滤器，用于处理模块卡片点击
  bool eventFilter(QObject *watched, QEvent *event) override;

private slots:
  void onLogoutClicked();
  void onCollectClicked();
  void updateTime();

private:
  void setupUI();
  void createMainPage();
  void setupConnections();
  void setupHeader();
  void setupMainContent();
  void setupCentralAI();
  void setupFunctionModules();
  void setupFooter();
  void setupAnimations();

  QWidget *createCircleWidget(int size, const QColor &color,
                              qreal opacity = 0.5);
  QWidget *createModuleCard(const QString &iconClass, const QString &title,
                            const QString &subtitle, const QString &colorClass);
  QString getIconColor(const QString &colorClass);
  QString getIconBackground(const QString &colorClass);

  QWidget *centralWidget;
  QWidget *mainPage;
  QPushButton *logoutButton;
  QPushButton *collectButton;
  QStackedWidget *stackedWidget;

  // 新增智能界面组件
  QWidget *headerWidget;
  QWidget *logoArea;
  QLabel *logoIcon;
  QLabel *logoText;
  QWidget *navMenu;
  QWidget *userArea;
  QLineEdit *searchBox;
  QPushButton *notificationButton;
  QPushButton *userAvatarButton;

  QWidget *contentArea;
  QLabel *welcomeLabel;
  QLabel *dateLabel;

  QWidget *centralAiArea;
  QWidget *aiCircleOuter;
  QWidget *aiCircleMiddle;
  QWidget *aiCircleInner;
  QLabel *aiRobot;

  QWidget *functionModules;
  QList<QWidget *> moduleCards;

  QWidget *footerWidget;
  QLabel *systemVersionLabel;
  QLabel *systemStatusLabel;
  QLabel *copyrightLabel;
  QLabel *timeLabel;

  QTimer *timer;
  QList<QPropertyAnimation *> animations;
};

#endif // MAINWINDOW_H