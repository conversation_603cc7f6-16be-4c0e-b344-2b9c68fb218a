#ifndef LOGINWINDOW_H
#define LOGINWINDOW_H

#include "api/api.h"
#include "utils/formutils.h"
#include "utils/storage.h"
#include "utils/toast.h"
#include <QEvent>
#include <QHBoxLayout>
#include <QLabel>
#include <QLineEdit>
#include <QObject>
#include <QPaintEvent>
#include <QPixmap>
#include <QPoint>
#include <QPushButton>
#include <QResizeEvent>
#include <QVBoxLayout>
#include <QWidget>

class LoginWindow : public QWidget {
  Q_OBJECT

public:
  explicit LoginWindow(QWidget *parent = nullptr);

protected:
  void paintEvent(QPaintEvent *event) override;
  bool eventFilter(QObject *watched, QEvent *event) override;
  void resizeEvent(QResizeEvent *event) override;

private slots:
  void onLoginClicked();
  void onForgotPasswordClicked();
  void onLoginSuccess();
  void onLoginFailed(const QString &error);
  void showMainWindow();

private:
  void centerWindow();
  void initUI();
  bool validateForm();
  void loadStyleSheet();
  void updateImageScale(); // 更新图片缩放

  QLineEdit *usernameEdit;
  QLineEdit *passwordEdit;
  QPushButton *loginButton;
  QPoint m_dragPosition;
  QLabel *m_imageLabel;      // 图片标签
  QPixmap m_bgImage;         // 背景图片
  QWidget *m_controlButtons; // 控制按钮组
};

#endif // LOGINWINDOW_H