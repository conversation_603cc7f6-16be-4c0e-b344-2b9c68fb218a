#ifndef API_H
#define API_H

#include <QString>
#include <QJsonObject>
#include <QJsonArray>
#include "utils/networkmanager.h"

class Api
{
public:
    // 用户相关接口
    static const QString LOGIN;            // 登录
    static const QString REGISTER;         // 注册
    static const QString GET_USER_INFO;    // 获取用户信息
    static const QString UPDATE_USER_INFO; // 更新用户信息
    static const QString LOGOUT;           // 退出登录

    // 用户相关方法
    static QNetworkReply *login(const QString &username, const QString &password);
    static QNetworkReply *registerUser(const QString &username, const QString &password, const QString &email);
    static QNetworkReply *getUserInfo();
    static QNetworkReply *updateUserInfo(const QJsonObject &userInfo);
    static QNetworkReply *logout();

private:
    static NetworkManager &networkManager;
};

#endif // API_H