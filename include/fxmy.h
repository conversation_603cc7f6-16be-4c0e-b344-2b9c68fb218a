#ifndef FXMY_H_
#define FXMY_H_

#include <stdint.h>

#if defined(_WIN32)
#if defined(FXMY_EXPORTS)
#define FXMY_API __declspec(dllexport)
#else
#define FXMY_API __declspec(dllimport)
#endif
#else
#define FXMY_API
#endif

#if defined(__cplusplus)
extern "C" {
#endif

typedef enum UIType {
  UI_TYPE_UNKNOWN = 0,
  UI_TYPE_QT,
  UI_TYPE_WIN32,
} UIType;

typedef struct Rect {
  int x;
  int y;
  int width;
  int height;
} Rect;

typedef uintptr_t WindowHandle;

typedef enum Status {
  STATUS_UNKNOWN = -1,
  STATUS_OK,
  STATUS_FAILED,
  STATUS_INVALID_PARAMETER,
  STATUS_ALREADY_INITIALIZED,
  STATUS_NOT_INITIALIZED,
  STATUS_NOT_IMPLEMENTED,
  STATUS_CALL_SEQUENCE_ERROR,
  STATUS_QT_QAPPLICATION_INSTANCE_INVALID,
  STATUS_INVALID_PARENT_WINDOW,
  STATUS_RESOURCE_NOT_FOUND,
} Status;

typedef enum State {
  STATE_UNKNOWN = 0,
  STATE_INITIALIZING,
  STATE_INITIALIZED_FAILED,
  STATE_INITIALIZED,
  STATE_RUNNING,
} State;

typedef enum CaptureState {
  CAPTURE_STATE_UNKNOWN = 0,
  CAPTURE_STATE_FAILED,
  CAPTURE_STATE_STARTING,
  CAPTURE_STATE_STARTED,
  CAPTURE_STATE_STOPPING,
  CAPTURE_STATE_STOPPED,
} CaptureState;

typedef enum DeviceState {
  DEVICE_STATE_UNKNOWN = 0,
  DEVICE_STATE_INITIALIZING,
  DEVICE_STATE_INITIALIZED_FAILED,
  DEVICE_STATE_INITIALIZED,
  DEVICE_STATE_RUNNING,
} DeviceState;

typedef enum Format {
  FORMAT_UNKNOWN = 0,
  FORMAT_RGB32,
} Format;

// align with 1 byte
#pragma pack(push, 1)
typedef struct ID {
  char id[32];
} ID;

typedef ID DeviceID;
typedef ID CaptureID;
typedef ID ItemID;
typedef ID TaskID;
typedef ID DocumentID;

typedef struct Image {
  int width;
  int height;
  Format format;
  uint8_t *data;
} Image;

typedef struct Document {
  DocumentID id;
  ItemID item_id;
  TaskID task_id;
  CaptureID capture_id;
  Image image;
} Document;

#pragma pack(pop)

typedef struct Device {
  DeviceID id;
} Device;

typedef struct CaptureDocumentItemData {
  ItemID item_id;
  TaskID task_id;
} CaptureDocumentItemData;

// declare the Callbacks

// on_device_enumerated
typedef void (*DeviceEnumeratedCallback)(Device *devices, int count,
                                         void *user_data);

// on_fxmy_state_changed
typedef void (*FxmyStateChangedCallback)(State state, void *user_data);

// on_fxmy_capture_state_changed
typedef void (*CaptureDocumentsStateChangedCallback)(
    CaptureID capture_id, CaptureState state,
    const CaptureDocumentItemData *items, int count, void *user_data);

// on_device_state_changed
typedef void (*DeviceStateChangedCallback)(const Device *device,
                                           DeviceState state, void *user_data);

typedef void (*GenTaskIDCallback)(CaptureID capture_id, ItemID item_id,
                                  TaskID *task_id, void *user_data);

typedef void (*DocumentReceivedCallback)(const struct Document *document,
                                         void *user_data);

typedef struct Config {
  UIType ui_type;
  int argc;
  char **argv;
  WindowHandle parent_window;
  Rect fxmy_main_window_rect;
  void *user_data;
  int document_cache_size;

  DeviceEnumeratedCallback device_enumerated_callback;
  FxmyStateChangedCallback fxmy_state_changed_callback;
  CaptureDocumentsStateChangedCallback capture_documents_state_changed_callback;
  DeviceStateChangedCallback device_state_changed_callback;
  GenTaskIDCallback gen_task_id_callback;
  DocumentReceivedCallback document_received_callback;
} Config;

Status FXMY_API fxmy_init(const Config *config);
Status FXMY_API fxmy_uninit();

WindowHandle FXMY_API fxmy_get_main_window();
Status FXMY_API fxmy_main_window_flush();

Status FXMY_API fxmy_capture_start();
Status FXMY_API fxmy_capture_stop();

State FXMY_API fxmy_get_state();

typedef struct Version {
  int major;
  int minor;
  int revision;
} Version;

void FXMY_API fxmy_get_version(Version *version);

#if defined(__cplusplus)
}
#endif

#endif // FXMY_H_