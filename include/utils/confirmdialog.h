#ifndef CONFIRMDIALOG_H
#define CONFIRMDIALOG_H

#include <QDialog>
#include <QLabel>
#include <QPushButton>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGraphicsDropShadowEffect>
#include <QPropertyAnimation>
#include <QGraphicsOpacityEffect>

class ConfirmDialog : public QDialog
{
    Q_OBJECT
    Q_PROPERTY(qreal opacity READ opacity WRITE setOpacity)

public:
    explicit ConfirmDialog(QWidget *parent = nullptr);
    ~ConfirmDialog();

    // 设置对话框内容
    void setTitle(const QString &title);
    void setMessage(const QString &message);
    void setConfirmText(const QString &text);
    void setCancelText(const QString &text);
    void setIcon(const QString &iconPath);

    // 显示对话框并返回结果
    static bool confirm(QWidget *parent, 
                       const QString &title, 
                       const QString &message,
                       const QString &confirmText = "确认",
                       const QString &cancelText = "取消",
                       const QString &iconPath = "");

protected:
    void showEvent(QShowEvent *event) override;
    void closeEvent(QCloseEvent *event) override;

private:
    void setupUI();
    void setupStyle();
    void setupAnimation();
    qreal opacity() const;
    void setOpacity(qreal opacity);

    QLabel *iconLabel;
    QLabel *titleLabel;
    QLabel *messageLabel;
    QPushButton *confirmButton;
    QPushButton *cancelButton;
    QVBoxLayout *mainLayout;
    QHBoxLayout *buttonLayout;
    QPropertyAnimation *showAnimation;
    QPropertyAnimation *hideAnimation;
    QGraphicsOpacityEffect *opacityEffect;
    qreal m_opacity;
};

#endif // CONFIRMDIALOG_H 