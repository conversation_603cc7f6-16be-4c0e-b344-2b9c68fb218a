#ifndef NETWORKMANAGER_H
#define NETWORKMANAGER_H

#include "tokenmanager.h"
#include <QJsonArray>
#include <QJsonDocument>
#include <QJsonObject>
#include <QMap>
#include <QNetworkAccessManager>
#include <QNetworkReply>
#include <QNetworkRequest>
#include <QObject>
#include <QString>
#include <QUrl>
#include <QUrlQuery>
#include <functional>

class NetworkManager : public QObject {
  Q_OBJECT

public:
  // 请求拦截器接口
  class RequestInterceptor {
  public:
    virtual ~RequestInterceptor() = default;
    virtual bool interceptRequest(QNetworkRequest &request) = 0;
  };

  // 响应拦截器接口
  class ResponseInterceptor {
  public:
    virtual ~ResponseInterceptor() = default;
    virtual bool interceptResponse(QNetworkReply *reply,
                                   QJsonObject &response) = 0;
  };

  static NetworkManager &getInstance();

  // 设置基础 URL
  void setBaseUrl(const QString &url);

  // 设置请求头
  void setHeader(const QString &key, const QString &value);

  // 清除所有请求头
  void clearHeaders();

  // 设置最大重试次数
  void setMaxRetries(int maxRetries);

  // 添加请求拦截器
  void addRequestInterceptor(RequestInterceptor *interceptor);

  // 添加响应拦截器
  void addResponseInterceptor(ResponseInterceptor *interceptor);

  // 移除请求拦截器
  void removeRequestInterceptor(RequestInterceptor *interceptor);

  // 移除响应拦截器
  void removeResponseInterceptor(ResponseInterceptor *interceptor);

  // 发送 POST 请求
  QNetworkReply *
  post(const QString &endpoint, const QJsonObject &data = QJsonObject(),
       const QMap<QString, QString> &queryParams = QMap<QString, QString>(),
       const QMap<QString, QString> &pathParams = QMap<QString, QString>(),
       std::function<void(const QJsonObject &)> onSuccess = nullptr,
       std::function<void(const QString &)> onError = nullptr,
       bool requireAuth = true);

  // 发送 GET 请求
  QNetworkReply *
  get(const QString &endpoint,
      const QMap<QString, QString> &queryParams = QMap<QString, QString>(),
      const QMap<QString, QString> &pathParams = QMap<QString, QString>(),
      std::function<void(const QJsonObject &)> onSuccess = nullptr,
      std::function<void(const QString &)> onError = nullptr,
      bool requireAuth = true);

  // 发送 PUT 请求
  QNetworkReply *
  put(const QString &endpoint, const QJsonObject &data = QJsonObject(),
      const QMap<QString, QString> &queryParams = QMap<QString, QString>(),
      const QMap<QString, QString> &pathParams = QMap<QString, QString>(),
      std::function<void(const QJsonObject &)> onSuccess = nullptr,
      std::function<void(const QString &)> onError = nullptr,
      bool requireAuth = true);

  // 发送 DELETE 请求
  QNetworkReply *
  del(const QString &endpoint,
      const QMap<QString, QString> &queryParams = QMap<QString, QString>(),
      const QMap<QString, QString> &pathParams = QMap<QString, QString>(),
      std::function<void(const QJsonObject &)> onSuccess = nullptr,
      std::function<void(const QString &)> onError = nullptr,
      bool requireAuth = true);

  // 设置认证 token
  void setAuthToken(const QString &token);

  // 设置环境
  void setEnvironment(const QString &env);

  // 设置和获取API前缀
  void setApiPrefix(const QString &prefix);
  QString getApiPrefix() const;

  QString getBaseUrl() const;

private:
  explicit NetworkManager(QObject *parent = nullptr);
  ~NetworkManager();

  // 单例模式
  static NetworkManager *instance;
  QNetworkAccessManager *manager;
  QString authToken;
  QString environment;
  QString baseUrl;
  QMap<QString, QString> headers;
  int maxRetries;
  QList<RequestInterceptor *> requestInterceptors;
  QList<ResponseInterceptor *> responseInterceptors;
  QString apiPrefix; // 添加API前缀成员变量
  QString apiBaseUrl;
  QString currentEnv; // 添加当前环境变量

  // 创建请求
  QNetworkRequest createRequest(const QString &endpoint,
                                const QMap<QString, QString> &queryParams,
                                const QMap<QString, QString> &pathParams,
                                bool requireAuth = true);

  // 处理响应
  void handleResponse(QNetworkReply *reply,
                      std::function<void(const QJsonObject &)> onSuccess,
                      std::function<void(const QString &)> onError,
                      int retryCount = 0);

  // 构建 URL
  QString buildUrl(const QString &endpoint,
                   const QMap<QString, QString> &queryParams,
                   const QMap<QString, QString> &pathParams);

  // 应用请求拦截器
  bool applyRequestInterceptors(QNetworkRequest &request);

  // 应用响应拦截器
  bool applyResponseInterceptors(QNetworkReply *reply, QJsonObject &response);

  // 处理网络错误
  void handleError(QNetworkReply *reply,
                   std::function<void(const QString &)> onError);

  // 发送请求的通用方法
  void sendRequest(const QString &endpoint, const QByteArray &method,
                   const QJsonObject &data,
                   const QMap<QString, QString> &queryParams,
                   const QMap<QString, QString> &pathParams,
                   std::function<void(const QJsonObject &)> onSuccess,
                   std::function<void(const QString &)> onError);

  // 替换路径参数
  QString replacePathParams(const QString &path,
                            const QMap<QString, QString> &params) const;

  void updateApiBaseUrl();
};

#endif // NETWORKMANAGER_H