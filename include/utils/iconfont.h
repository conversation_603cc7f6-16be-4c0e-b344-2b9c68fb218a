#ifndef ICONFONT_H
#define ICONFONT_H

#include <QObject>
#include <QFont>
#include <QFontDatabase>
#include <QIcon>
#include <QMap>

class IconFont : public QObject
{
    Q_OBJECT

public:
    static IconFont& getInstance();

    // 初始化字体
    void initFont(const QString& fontPath);
    
    // 获取图标
    QIcon getIcon(const QString& iconName, int size = 16, const QColor& color = Qt::black);
    
    // 获取字体
    QFont getFont(int size = 16);

private:
    explicit IconFont(QObject *parent = nullptr);
    ~IconFont();

    int fontId;
    QMap<QString, int> iconMap;
};

#endif // ICONFONT_H 