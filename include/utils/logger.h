#pragma once

#include <QDateTime>
#include <QDir>
#include <QFile>
#include <QLoggingCategory>
#include <QObject>
#include <QString>
#include <QTextStream>

class Logger : public QObject {
  Q_OBJECT

public:
  // 日志级别枚举
  enum LogLevel {
    Debug = QtDebugMsg,
    Info = QtInfoMsg,
    Warning = QtWarningMsg,
    Error = QtCriticalMsg,
    Fatal = QtFatalMsg
  };

  // 日志配置结构体
  struct Config {
    int maxDays;
    qint64 maxFileSize;
    int maxBackupFiles;
    QString pattern;
    LogLevel level;

    Config()
        : maxDays(30), maxFileSize(10 * 1024 * 1024), // 10MB
          maxBackupFiles(10),
          pattern("%d{yyyy-MM-dd HH:mm:ss.zzz} [%p] %c - %m%n"), level(Debug) {}
  };

  static Logger &getInstance();

  // 初始化日志系统
  void init(const QString &logPath = QString(),
            const Config &config = Config());

  // 设置日志级别
  void setLogLevel(LogLevel level);

  // 设置日志配置
  void setConfig(const Config &config);

  // 获取当前配置
  Config getConfig() const;

  // 清理过期日志
  void cleanOldLogs();

  // 记录日志
  void log(LogLevel level, const QString &message);

private:
  explicit Logger(QObject *parent = nullptr);
  ~Logger();

  // 禁用拷贝和赋值
  Logger(const Logger &) = delete;
  Logger &operator=(const Logger &) = delete;

  // 创建日志目录
  void createLogDirectory(const QString &path);

  // 获取日志文件名
  QString getLogFileName() const;

  // 检查并轮转日志文件
  void checkAndRotateLogFile();

  // 日志消息处理函数
  static void messageHandler(QtMsgType type, const QMessageLogContext &context,
                             const QString &msg);

  QFile *logFile;
  QTextStream *logStream;
  QString logPath;
  Config config;
  static Logger *instance;
};

// 定义日志类别
Q_DECLARE_LOGGING_CATEGORY(logDebug)
Q_DECLARE_LOGGING_CATEGORY(logInfo)
Q_DECLARE_LOGGING_CATEGORY(logWarning)
Q_DECLARE_LOGGING_CATEGORY(logError)