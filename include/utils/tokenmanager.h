#ifndef TOKENMANAGER_H
#define TOKENMANAGER_H

#include <QString>
#include <QSettings>
#include <QObject>
#include <QDateTime>

class TokenManager : public QObject
{
    Q_OBJECT

public:
    static TokenManager& getInstance();

    // 设置 token
    void setToken(const QString& token, int expiresIn = 0);
    
    // 获取 token
    QString getToken() const;
    
    // 清除 token
    void clearToken();
    
    // 检查是否有 token
    bool hasToken() const;

    // 检查 token 是否过期
    bool isTokenExpired() const;

    // 获取 token 过期时间
    QDateTime getExpirationTime() const;

signals:
    // token 过期信号
    void tokenExpired();

private:
    TokenManager(QObject* parent = nullptr);
    ~TokenManager();
    
    // 禁用拷贝和赋值
    TokenManager(const TokenManager&) = delete;
    TokenManager& operator=(const TokenManager&) = delete;

    QSettings* settings;
    static const QString TOKEN_KEY;
    static const QString EXPIRATION_KEY;
};

#endif // TOKENMANAGER_H 