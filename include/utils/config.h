#ifndef CONFIG_H
#define CONFIG_H

#include <QObject>
#include <QSettings>
#include <QString>
#include <QVariant>

/**
 * @brief 配置管理类
 * 类似于前端的config/env文件，用于管理应用程序的配置项
 */
class Config : public QObject {
    Q_OBJECT

public:
    static Config& getInstance();
    
    // 应用程序配置
    struct AppConfig {
        QString appName;
        QString version;
        QString windowTitle;
        bool debugMode;
        QString logLevel;
    };
    
    // UI配置
    struct UIConfig {
        QString titleText;
        int titleFontSize;
        QString primaryColor;
        QString backgroundColor;
        int windowWidth;
        int windowHeight;
    };
    
    // API配置
    struct APIConfig {
        QString baseUrl;
        QString loginEndpoint;
        QString logoutEndpoint;
        int timeout;
        bool useHttps;
    };
    
    // 获取配置
    AppConfig getAppConfig() const;
    UIConfig getUIConfig() const;
    APIConfig getAPIConfig() const;
    
    // 设置配置
    void setAppConfig(const AppConfig& config);
    void setUIConfig(const UIConfig& config);
    void setAPIConfig(const APIConfig& config);
    
    // 通用配置方法
    QVariant getValue(const QString& key, const QVariant& defaultValue = QVariant()) const;
    void setValue(const QString& key, const QVariant& value);
    
    // 加载和保存配置
    void loadFromFile(const QString& filePath = "");
    void saveToFile(const QString& filePath = "") const;
    
    // 重置为默认配置
    void resetToDefaults();

private:
    Config(QObject* parent = nullptr);
    ~Config() = default;
    Config(const Config&) = delete;
    Config& operator=(const Config&) = delete;
    
    void initializeDefaults();
    
    QSettings* m_settings;
    QString m_configFilePath;
};

#endif // CONFIG_H
