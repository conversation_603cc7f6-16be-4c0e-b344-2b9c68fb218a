#ifndef STORAGE_H
#define STORAGE_H

#include <QString>
#include <QSettings>
#include <QObject>
#include <QVariant>
#include <QJsonObject>
#include <QJsonArray>
#include <QJsonDocument>

class Storage : public QObject
{
    Q_OBJECT

public:
    static Storage& getInstance();

    // 设置值
    void setItem(const QString& key, const QVariant& value);
    
    // 获取值
    QVariant getItem(const QString& key, const QVariant& defaultValue = QVariant()) const;
    
    // 移除值
    void removeItem(const QString& key);
    
    // 清除所有值
    void clear();
    
    // 获取所有键
    QStringList keys() const;
    
    // 检查键是否存在
    bool hasItem(const QString& key) const;
    
    // 获取存储大小
    int size() const;

    // JSON 相关方法
    void setJson(const QString& key, const QJsonObject& json);
    QJsonObject getJson(const QString& key) const;
    void setJsonArray(const QString& key, const QJsonArray& array);
    QJsonArray getJsonArray(const QString& key) const;

signals:
    // 存储变化信号
    void storageChanged(const QString& key, const QVariant& value);
    void storageRemoved(const QString& key);
    void storageCleared();

private:
    Storage(QObject* parent = nullptr);
    ~Storage();
    
    // 禁用拷贝和赋值
    Storage(const Storage&) = delete;
    Storage& operator=(const Storage&) = delete;

    QSettings* settings;
    static const QString STORAGE_PREFIX;
};

#endif // STORAGE_H 