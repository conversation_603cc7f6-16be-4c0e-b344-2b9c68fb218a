#ifndef TOAST_H
#define TOAST_H

#include <QGraphicsOpacityEffect>
#include <QLabel>
#include <QPropertyAnimation>
#include <QTimer>
#include <QWidget>

class Toast : public QWidget {
  Q_OBJECT
  Q_PROPERTY(qreal opacity READ opacity WRITE setOpacity)

public:
  enum Type { Success, Error, Warning, Info };

  explicit Toast(QWidget *parent = nullptr);
  ~Toast();

  static void success(const QString &message, QWidget *parent = nullptr,
                      int duration = 3000);
  static void error(const QString &message, QWidget *parent = nullptr,
                    int duration = 3000);
  static void warning(const QString &message, QWidget *parent = nullptr,
                      int duration = 3000);
  static void info(const QString &message, QWidget *parent = nullptr,
                   int duration = 3000);

  void showMessage(Type type, const QString &message, int duration = 3000);

  qreal opacity() const;
  void setOpacity(qreal opacity);

private:
  QLabel *messageLabel;
  QTimer *timer;
  QPropertyAnimation *animation;
  QGraphicsOpacityEffect *opacityEffect;
  qreal m_opacity;
};

#endif // TOAST_H