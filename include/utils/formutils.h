#ifndef FORMUTILS_H
#define FORMUTILS_H

#include <QWidget>
#include <QLineEdit>
#include <QTextEdit>
#include <QComboBox>
#include <QCheckBox>
#include <QRadioButton>
#include <QSpinBox>
#include <QDoubleSpinBox>
#include <QDateEdit>
#include <QTimeEdit>
#include <QDateTimeEdit>
#include <QList>
#include <functional>

class FormUtils
{
public:
    // 重置单个输入框
    static void resetLineEdit(QLineEdit* edit);
    
    // 重置多个输入框
    static void resetLineEdits(const QList<QLineEdit*>& edits);
    
    // 重置文本区域
    static void resetTextEdit(QTextEdit* edit);
    
    // 重置下拉框
    static void resetComboBox(QComboBox* combo);
    
    // 重置复选框
    static void resetCheckBox(QCheckBox* checkBox);
    
    // 重置单选按钮
    static void resetRadioButton(QRadioButton* radio);
    
    // 重置数字输入框
    static void resetSpinBox(QSpinBox* spinBox);
    
    // 重置浮点数输入框
    static void resetDoubleSpinBox(QDoubleSpinBox* spinBox);
    
    // 重置日期选择器
    static void resetDateEdit(QDateEdit* dateEdit);
    
    // 重置时间选择器
    static void resetTimeEdit(QTimeEdit* timeEdit);
    
    // 重置日期时间选择器
    static void resetDateTimeEdit(QDateTimeEdit* dateTimeEdit);
    
    // 重置所有表单控件
    static void resetForm(const QList<QWidget*>& widgets);

    // 基础验证方法
    static bool validateRequired(const QString& value, const QString& fieldName);
    static bool validateLength(const QString& value, int minLength, int maxLength, const QString& fieldName);
    static bool validateEmail(const QString& email);
    static bool validatePhone(const QString& phone);

    // 自定义验证方法
    static bool validateCustom(const QString& value, 
                             const QString& fieldName,
                             std::function<bool(const QString&)> validator,
                             const QString& errorMessage = QString());

    // 组合验证方法
    static bool validateWithRules(const QString& value,
                                const QString& fieldName,
                                const QList<std::function<bool(const QString&)>>& validators,
                                const QList<QString>& errorMessages);

    // 验证整个表单
    static bool validateForm(const QMap<QString, QString>& formData,
                           const QMap<QString, QList<std::function<bool(const QString&)>>>& rules,
                           const QMap<QString, QList<QString>>& errorMessages);

private:
    static void showError(const QString& message);
};

#endif // FORMUTILS_H 