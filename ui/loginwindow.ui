<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>LoginWindow</class>
 <widget class="QWidget" name="LoginWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>944</width>
    <height>600</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Aiwuu在线课堂 - 登录</string>
  </property>
  <layout class="QVBoxLayout" name="mainLayout">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>20</number>
   </property>
   <property name="topMargin">
    <number>20</number>
   </property>
   <property name="rightMargin">
    <number>20</number>
   </property>
   <property name="bottomMargin">
    <number>20</number>
   </property>
   <item>
    <layout class="QVBoxLayout" name="containerLayout">
     <property name="spacing">
      <number>0</number>
     </property>
     <property name="leftMargin">
      <number>0</number>
     </property>
     <property name="topMargin">
      <number>0</number>
     </property>
     <property name="rightMargin">
      <number>0</number>
     </property>
     <property name="bottomMargin">
      <number>0</number>
     </property>
     <item>
      <layout class="QHBoxLayout" name="titleBarLayout">
       <property name="spacing">
        <number>0</number>
       </property>
       <property name="leftMargin">
        <number>0</number>
       </property>
       <property name="topMargin">
        <number>0</number>
       </property>
       <property name="rightMargin">
        <number>0</number>
       </property>
       <property name="bottomMargin">
        <number>0</number>
       </property>
       <item>
        <spacer name="titleSpacer">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <layout class="QHBoxLayout" name="controlLayout">
         <property name="spacing">
          <number>8</number>
         </property>
         <property name="leftMargin">
          <number>0</number>
         </property>
         <property name="topMargin">
          <number>0</number>
         </property>
         <property name="rightMargin">
          <number>0</number>
         </property>
         <property name="bottomMargin">
          <number>0</number>
         </property>
         <item>
          <widget class="QPushButton" name="closeButton">
           <property name="minimumSize">
            <size>
             <width>12</width>
             <height>12</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>12</width>
             <height>12</height>
            </size>
           </property>
           <property name="toolTip">
            <string>关闭</string>
           </property>
           <property name="text">
            <string>×</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="minButton">
           <property name="minimumSize">
            <size>
             <width>12</width>
             <height>12</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>12</width>
             <height>12</height>
            </size>
           </property>
           <property name="toolTip">
            <string>最小化</string>
           </property>
           <property name="text">
            <string>−</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="maxButton">
           <property name="minimumSize">
            <size>
             <width>12</width>
             <height>12</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>12</width>
             <height>12</height>
            </size>
           </property>
           <property name="toolTip">
            <string>最大化</string>
           </property>
           <property name="text">
            <string>□</string>
           </property>
          </widget>
         </item>
        </layout>
       </item>
      </layout>
     </item>
     <item>
      <layout class="QHBoxLayout" name="contentLayout">
       <property name="spacing">
        <number>0</number>
       </property>
       <property name="leftMargin">
        <number>0</number>
       </property>
       <property name="topMargin">
        <number>0</number>
       </property>
       <property name="rightMargin">
        <number>0</number>
       </property>
       <property name="bottomMargin">
        <number>0</number>
       </property>
       <item>
        <layout class="QVBoxLayout" name="leftLayout">
         <property name="spacing">
          <number>0</number>
         </property>
         <property name="leftMargin">
          <number>0</number>
         </property>
         <property name="topMargin">
          <number>0</number>
         </property>
         <property name="rightMargin">
          <number>0</number>
         </property>
         <property name="bottomMargin">
          <number>0</number>
         </property>
         <item>
          <widget class="QLabel" name="imageLabel">
           <property name="text">
            <string/>
           </property>
           <property name="scaledContents">
            <bool>false</bool>
           </property>
           <property name="alignment">
            <set>Qt::AlignCenter</set>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <layout class="QVBoxLayout" name="formLayout">
         <property name="spacing">
          <number>0</number>
         </property>
         <property name="leftMargin">
          <number>60</number>
         </property>
         <property name="topMargin">
          <number>60</number>
         </property>
         <property name="rightMargin">
          <number>60</number>
         </property>
         <property name="bottomMargin">
          <number>60</number>
         </property>
         <item>
          <layout class="QVBoxLayout" name="titleLayout">
           <property name="spacing">
            <number>6</number>
           </property>
           <property name="leftMargin">
            <number>0</number>
           </property>
           <property name="topMargin">
            <number>0</number>
           </property>
           <property name="rightMargin">
            <number>0</number>
           </property>
           <property name="bottomMargin">
            <number>0</number>
           </property>
           <item>
            <widget class="QLabel" name="titleLabel">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Minimum" vsizetype="Preferred">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="text">
              <string>Aiwuu在线课堂教师端</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignCenter</set>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item>
          <spacer name="titleFormSpacer">
           <property name="orientation">
            <enum>Qt::Vertical</enum>
           </property>
           <property name="sizeType">
            <enum>QSizePolicy::Fixed</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>20</width>
             <height>8</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <layout class="QVBoxLayout" name="formContentLayout">
           <property name="spacing">
            <number>0</number>
           </property>
           <property name="leftMargin">
            <number>0</number>
           </property>
           <property name="topMargin">
            <number>0</number>
           </property>
           <property name="rightMargin">
            <number>0</number>
           </property>
           <property name="bottomMargin">
            <number>0</number>
           </property>
           <item>
            <layout class="QVBoxLayout" name="usernameLayout">
             <property name="spacing">
              <number>4</number>
             </property>
             <property name="leftMargin">
              <number>0</number>
             </property>
             <property name="topMargin">
              <number>0</number>
             </property>
             <property name="rightMargin">
              <number>0</number>
             </property>
             <property name="bottomMargin">
              <number>0</number>
             </property>
             <item>
              <widget class="QLabel" name="usernameLabel">
               <property name="text">
                <string>用户名</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLineEdit" name="usernameEdit">
               <property name="minimumSize">
                <size>
                 <width>0</width>
                 <height>50</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>16777215</width>
                 <height>50</height>
                </size>
               </property>
               <property name="placeholderText">
                <string>请输入用户名</string>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item>
            <spacer name="usernamePasswordSpacer">
             <property name="orientation">
              <enum>Qt::Vertical</enum>
             </property>
             <property name="sizeType">
              <enum>QSizePolicy::Fixed</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>20</width>
               <height>6</height>
              </size>
             </property>
            </spacer>
           </item>
           <item>
            <layout class="QVBoxLayout" name="passwordLayout">
             <property name="spacing">
              <number>4</number>
             </property>
             <property name="leftMargin">
              <number>0</number>
             </property>
             <property name="topMargin">
              <number>0</number>
             </property>
             <property name="rightMargin">
              <number>0</number>
             </property>
             <property name="bottomMargin">
              <number>0</number>
             </property>
             <item>
              <widget class="QLabel" name="passwordLabel">
               <property name="text">
                <string>密码</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLineEdit" name="passwordEdit">
               <property name="minimumSize">
                <size>
                 <width>0</width>
                 <height>50</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>16777215</width>
                 <height>50</height>
                </size>
               </property>
               <property name="echoMode">
                <enum>QLineEdit::Password</enum>
               </property>
               <property name="placeholderText">
                <string>请输入密码</string>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item>
            <spacer name="passwordLoginSpacer">
             <property name="orientation">
              <enum>Qt::Vertical</enum>
             </property>
             <property name="sizeType">
              <enum>QSizePolicy::Fixed</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>20</width>
               <height>40</height>
              </size>
             </property>
            </spacer>
           </item>
           <item>
            <widget class="QPushButton" name="loginButton">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>45</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>16777215</width>
               <height>45</height>
              </size>
             </property>
             <property name="text">
              <string>登录</string>
             </property>
            </widget>
           </item>
           <item>
            <spacer name="loginForgotSpacer">
             <property name="orientation">
              <enum>Qt::Vertical</enum>
             </property>
             <property name="sizeType">
              <enum>QSizePolicy::Fixed</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>20</width>
               <height>6</height>
              </size>
             </property>
            </spacer>
           </item>
           <item>
            <layout class="QHBoxLayout" name="forgotLayout">
             <item>
              <spacer name="forgotLeftSpacer">
               <property name="orientation">
                <enum>Qt::Horizontal</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>40</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
             <item>
              <widget class="QPushButton" name="forgotButton">
               <property name="text">
                <string>忘记密码</string>
               </property>
              </widget>
             </item>
            </layout>
           </item>
          </layout>
         </item>
         <item>
          <spacer name="formBottomSpacer">
           <property name="orientation">
            <enum>Qt::Vertical</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>20</width>
             <height>40</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </item>
      </layout>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
