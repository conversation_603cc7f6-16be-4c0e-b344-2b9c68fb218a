<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1200</width>
    <height>800</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>智能开发平台</string>
  </property>
  <property name="objectName">
   <string>MainWindow</string>
  </property>
  <property name="styleSheet">
   <string notr="true">background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #2196f3, stop:0.5 #42a5f5, stop:1 #64b5f6);</string>
  </property>
  <widget class="QWidget" name="centralWidget">
   <property name="objectName">
    <string>centralWidget</string>
   </property>
   <layout class="QVBoxLayout" name="mainLayout">
    <property name="spacing">
     <number>0</number>
    </property>
    <property name="leftMargin">
     <number>0</number>
    </property>
    <property name="topMargin">
     <number>0</number>
    </property>
    <property name="rightMargin">
     <number>0</number>
    </property>
    <property name="bottomMargin">
     <number>0</number>
    </property>
    <item>
     <widget class="QStackedWidget" name="stackedWidget">
      <property name="objectName">
       <string>stackedWidget</string>
      </property>
      <property name="currentIndex">
       <number>0</number>
      </property>
      <widget class="QWidget" name="mainPage">
       <property name="objectName">
        <string>mainPage</string>
       </property>
       <layout class="QVBoxLayout" name="mainPageLayout">
        <property name="spacing">
         <number>20</number>
        </property>
        <property name="leftMargin">
         <number>40</number>
        </property>
        <property name="topMargin">
         <number>40</number>
        </property>
        <property name="rightMargin">
         <number>40</number>
        </property>
        <property name="bottomMargin">
         <number>40</number>
        </property>
        <item>
         <widget class="QLabel" name="welcomeLabel">
          <property name="text">
           <string>欢迎使用智能开发平台</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignCenter</set>
          </property>
          <property name="objectName">
           <string>welcomeLabel</string>
          </property>
          <property name="styleSheet">
           <string>color: white; font-size: 24px; font-weight: bold;</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="dateLabel">
          <property name="text">
           <string>当前日期</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignCenter</set>
          </property>
          <property name="objectName">
           <string>dateLabel</string>
          </property>
          <property name="styleSheet">
           <string>color: rgba(255, 255, 255, 0.8); font-size: 14px;</string>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="mainPageSpacer">
          <property name="orientation">
           <enum>Qt::Vertical</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>20</width>
            <height>40</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QWidget" name="contentArea">
          <property name="objectName">
           <string>contentArea</string>
          </property>
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>400</height>
           </size>
          </property>
          <layout class="QVBoxLayout" name="contentLayout">
           <property name="spacing">
            <number>20</number>
           </property>
           <property name="leftMargin">
            <number>20</number>
           </property>
           <property name="topMargin">
            <number>20</number>
           </property>
           <property name="rightMargin">
            <number>20</number>
           </property>
           <property name="bottomMargin">
            <number>20</number>
           </property>
           <item>
            <widget class="QLabel" name="contentLabel">
             <property name="text">
              <string>智能平台功能区域</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignCenter</set>
             </property>
             <property name="objectName">
              <string>contentLabel</string>
             </property>
             <property name="styleSheet">
              <string>color: white; font-size: 16px; background-color: rgba(255, 255, 255, 0.1); padding: 20px; border-radius: 8px;</string>
             </property>
            </widget>
           </item>
           <item>
            <layout class="QHBoxLayout" name="actionButtonsLayout">
             <property name="spacing">
              <number>10</number>
             </property>
             <item>
              <spacer name="actionButtonsLeftSpacer">
               <property name="orientation">
                <enum>Qt::Horizontal</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>40</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
             <item>
              <widget class="QPushButton" name="collectButton">
               <property name="text">
                <string>采集</string>
               </property>
               <property name="objectName">
                <string>collectButton</string>
               </property>
               <property name="minimumSize">
                <size>
                 <width>120</width>
                 <height>40</height>
                </size>
               </property>
               <property name="styleSheet">
                <string>QPushButton {
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    font-weight: bold;
}
QPushButton:hover {
    background-color: rgba(255, 255, 255, 0.3);
}
QPushButton:pressed {
    background-color: rgba(255, 255, 255, 0.15);
}</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="courseButton">
               <property name="text">
                <string>课程管理</string>
               </property>
               <property name="objectName">
                <string>courseButton</string>
               </property>
               <property name="minimumSize">
                <size>
                 <width>120</width>
                 <height>40</height>
                </size>
               </property>
               <property name="styleSheet">
                <string>QPushButton {
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    font-weight: bold;
}
QPushButton:hover {
    background-color: rgba(255, 255, 255, 0.3);
}
QPushButton:pressed {
    background-color: rgba(255, 255, 255, 0.15);
}</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="studentButton">
               <property name="text">
                <string>学生管理</string>
               </property>
               <property name="objectName">
                <string>studentButton</string>
               </property>
               <property name="minimumSize">
                <size>
                 <width>120</width>
                 <height>40</height>
                </size>
               </property>
               <property name="styleSheet">
                <string>QPushButton {
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    font-weight: bold;
}
QPushButton:hover {
    background-color: rgba(255, 255, 255, 0.3);
}
QPushButton:pressed {
    background-color: rgba(255, 255, 255, 0.15);
}</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="settingsButton">
               <property name="text">
                <string>系统设置</string>
               </property>
               <property name="objectName">
                <string>settingsButton</string>
               </property>
               <property name="minimumSize">
                <size>
                 <width>120</width>
                 <height>40</height>
                </size>
               </property>
               <property name="styleSheet">
                <string>QPushButton {
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    font-weight: bold;
}
QPushButton:hover {
    background-color: rgba(255, 255, 255, 0.3);
}
QPushButton:pressed {
    background-color: rgba(255, 255, 255, 0.15);
}</string>
               </property>
              </widget>
             </item>
             <item>
              <spacer name="actionButtonsRightSpacer">
               <property name="orientation">
                <enum>Qt::Horizontal</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>40</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
            </layout>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <spacer name="bottomSpacer">
          <property name="orientation">
           <enum>Qt::Vertical</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>20</width>
            <height>40</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </widget>
     </widget>
    </item>
    <item>
     <layout class="QHBoxLayout" name="toolbarLayout">
      <property name="spacing">
       <number>10</number>
      </property>
      <property name="leftMargin">
       <number>20</number>
      </property>
      <property name="topMargin">
       <number>10</number>
      </property>
      <property name="rightMargin">
       <number>20</number>
      </property>
      <property name="bottomMargin">
       <number>10</number>
      </property>
      <item>
       <spacer name="toolbarLeftSpacer">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="QLabel" name="statusLabel">
        <property name="text">
         <string>就绪</string>
        </property>
        <property name="objectName">
         <string>statusLabel</string>
        </property>
        <property name="styleSheet">
         <string>color: white; font-size: 12px;</string>
        </property>
       </widget>
      </item>
      <item>
       <spacer name="toolbarCenterSpacer">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="QPushButton" name="logoutButton">
        <property name="text">
         <string>退出登录</string>
        </property>
        <property name="objectName">
         <string>logoutButton</string>
        </property>
        <property name="minimumSize">
         <size>
          <width>100</width>
          <height>35</height>
         </size>
        </property>
        <property name="styleSheet">
         <string>QPushButton {
    background-color: rgba(220, 53, 69, 0.8);
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    font-weight: bold;
}
QPushButton:hover {
    background-color: rgba(200, 35, 51, 0.9);
}
QPushButton:pressed {
    background-color: rgba(189, 33, 48, 1);
}</string>
        </property>
       </widget>
      </item>
     </layout>
    </item>
   </layout>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui> 