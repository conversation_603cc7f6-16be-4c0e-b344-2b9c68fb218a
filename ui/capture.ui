<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Capture</class>
 <widget class="QWidget" name="Capture">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>800</width>
    <height>600</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>数据采集 - Capture</string>
  </property>
  <property name="objectName">
   <string>Capture</string>
  </property>
  <layout class="QVBoxLayout" name="mainLayout">
   <property name="spacing">
    <number>15</number>
   </property>
   <property name="leftMargin">
    <number>20</number>
   </property>
   <property name="topMargin">
    <number>20</number>
   </property>
   <property name="rightMargin">
    <number>20</number>
   </property>
   <property name="bottomMargin">
    <number>20</number>
   </property>
   <item>
    <layout class="QHBoxLayout" name="headerLayout">
     <item>
      <widget class="QLabel" name="titleLabel">
       <property name="text">
        <string>数据采集系统</string>
       </property>
       <property name="objectName">
        <string>titleLabel</string>
       </property>
       <property name="styleSheet">
        <string>font-size: 24px; font-weight: bold; color: #333;</string>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="headerSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="backButton">
       <property name="text">
        <string>返回</string>
       </property>
       <property name="objectName">
        <string>backButton</string>
       </property>
       <property name="minimumSize">
        <size>
         <width>80</width>
         <height>35</height>
        </size>
       </property>
       <property name="styleSheet">
        <string>QPushButton {
    background-color: #6c757d;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 14px;
}
QPushButton:hover {
    background-color: #5a6268;
}
QPushButton:pressed {
    background-color: #545b62;
}</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QLabel" name="statusLabel">
     <property name="text">
      <string>就绪</string>
     </property>
     <property name="objectName">
      <string>statusLabel</string>
     </property>
     <property name="styleSheet">
      <string>font-size: 16px; color: #28a745; font-weight: bold;</string>
     </property>
    </widget>
   </item>
   <item>
    <layout class="QHBoxLayout" name="buttonLayout">
     <item>
      <widget class="QPushButton" name="startButton">
       <property name="text">
        <string>开始采集</string>
       </property>
       <property name="objectName">
        <string>startButton</string>
       </property>
       <property name="minimumSize">
        <size>
         <width>120</width>
         <height>40</height>
        </size>
       </property>
       <property name="styleSheet">
        <string>QPushButton {
    background-color: #28a745;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    font-weight: bold;
}
QPushButton:hover {
    background-color: #218838;
}
QPushButton:pressed {
    background-color: #1e7e34;
}
QPushButton:disabled {
    background-color: #6c757d;
}</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="stopButton">
       <property name="text">
        <string>停止采集</string>
       </property>
       <property name="objectName">
        <string>stopButton</string>
       </property>
       <property name="enabled">
        <bool>false</bool>
       </property>
       <property name="minimumSize">
        <size>
         <width>120</width>
         <height>40</height>
        </size>
       </property>
       <property name="styleSheet">
        <string>QPushButton {
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    font-weight: bold;
}
QPushButton:hover {
    background-color: #c82333;
}
QPushButton:pressed {
    background-color: #bd2130;
}
QPushButton:disabled {
    background-color: #6c757d;
}</string>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="buttonSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QProgressBar" name="progressBar">
     <property name="value">
      <number>0</number>
     </property>
     <property name="objectName">
      <string>progressBar</string>
     </property>
     <property name="styleSheet">
      <string>QProgressBar {
    border: 2px solid #ddd;
    border-radius: 5px;
    text-align: center;
    font-weight: bold;
}
QProgressBar::chunk {
    background-color: #28a745;
    border-radius: 3px;
}</string>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QTextEdit" name="logTextEdit">
     <property name="readOnly">
      <bool>true</bool>
     </property>
     <property name="objectName">
      <string>logTextEdit</string>
     </property>
     <property name="styleSheet">
      <string>QTextEdit {
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 10px;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    background-color: #f8f9fa;
}</string>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui> 