cmake_minimum_required(VERSION 3.8)

project(aiwuu-classroom VERSION 1.0.0 LANGUAGES CXX)

# 建议Qt Creator使用项目内的build目录
if(NOT DEFINED CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE "Debug" CACHE STRING "Build type" FORCE)
endif()

# 设置默认构建目录为项目内的build目录
if(CMAKE_SOURCE_DIR STREQUAL CMAKE_CURRENT_SOURCE_DIR)
    set(CMAKE_BINARY_DIR "${CMAKE_SOURCE_DIR}/build" CACHE PATH "Build directory" FORCE)
endif()

# 设置 C++ 标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 设置架构
if(APPLE)
    if(CMAKE_SYSTEM_PROCESSOR MATCHES "arm64")
        set(CMAKE_OSX_ARCHITECTURES "arm64")
    else()
        set(CMAKE_OSX_ARCHITECTURES "x86_64")
    endif()
endif()

# Qt路径自动检测 - 优雅的方式
# 1. 优先使用CMAKE_PREFIX_PATH（Qt Creator会自动设置）
# 2. 其次尝试通过brew查找（仅macOS）
# 3. 最后依赖CMake的标准查找机制
if(NOT CMAKE_PREFIX_PATH AND APPLE)
    # 仅在macOS上且CMAKE_PREFIX_PATH未设置时，尝试通过Homebrew查找Qt5
    find_program(BREW_COMMAND brew)
    if(BREW_COMMAND)
        execute_process(
            COMMAND ${BREW_COMMAND} --prefix qt@5
            OUTPUT_VARIABLE QT_HOMEBREW_PATH
            OUTPUT_STRIP_TRAILING_WHITESPACE
            ERROR_QUIET
            RESULT_VARIABLE BREW_RESULT
        )
        if(BREW_RESULT EQUAL 0 AND QT_HOMEBREW_PATH)
            set(CMAKE_PREFIX_PATH "${QT_HOMEBREW_PATH}")
            message(STATUS "Found Qt5 via Homebrew: ${QT_HOMEBREW_PATH}")
        endif()
    endif()
endif()

# 启用 Qt 的自动特性
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC ON)
set(CMAKE_AUTOUIC ON)

# 设置UI文件搜索路径
set(CMAKE_AUTOUIC_SEARCH_PATHS ${CMAKE_CURRENT_SOURCE_DIR}/ui)

# 优化Qt Creator支持
set(CMAKE_AUTOMOC_MOC_OPTIONS "-nn")
set(CMAKE_AUTOMOC_MACRO_NAMES "Q_OBJECT" "Q_GADGET" "Q_NAMESPACE")

# 设置autogen目录位置（隐藏在build目录中）
# 这些属性将在目标创建后设置

# Qt Creator UI设计器支持
set_property(GLOBAL PROPERTY AUTOGEN_SOURCE_GROUP "Generated Files")
set_property(GLOBAL PROPERTY AUTOGEN_TARGETS_FOLDER "CMakeTargets")

# 确保 Qt Core 包含 zlib 支持

# 查找 Qt 包 - 让CMake自动处理路径查找
find_package(Qt5 COMPONENTS Core Gui Widgets Network REQUIRED)

# 显示找到的Qt信息
message(STATUS "Found Qt5: ${Qt5_VERSION}")
message(STATUS "Qt5 installation path: ${Qt5_DIR}")

# UI模式选项
option(USE_UI_FILES "Use UI files instead of pure code" OFF)

# Qt使用选项
option(USE_QT "Use Qt framework" ON)

# 开发环境设置选项
option(RUN_DEV_SETUP "Run development environment setup before build" OFF)

# 收集源文件
if(USE_UI_FILES)
    # UI文件模式：使用UI版本的文件
    file(GLOB SOURCES
        "src/core/main_ui.cc"
        "src/core/loginwindow_ui.cc"
        "src/core/mainwindow_ui.cc"
        "src/core/capture_ui.cc"
        "src/utils/*.cc"
        "src/api/*.cc"
    )
    # 收集UI文件
    file(GLOB UI_FILES
        "ui/*.ui"
    )
    message(STATUS "Building with UI files")
else()
    # 纯代码模式：使用纯代码版本的文件
    file(GLOB SOURCES
        "src/core/main.cc"
        "src/core/loginwindow.cc"
        "src/core/mainwindow.cc"
        "src/core/capture.cc"
        "src/utils/*.cc"
        "src/api/*.cc"
    )
    # 纯代码模式不需要UI文件
    set(UI_FILES "")
    message(STATUS "Building with pure code")
endif()

# 收集头文件
if(USE_UI_FILES)
    # UI文件模式：使用UI版本的头文件
    file(GLOB HEADERS
        "include/core/loginwindow_ui.h"
        "include/core/mainwindow_ui.h"
        "include/core/capture_ui.h"
        "include/utils/*.h"
        "include/api/*.h"
    )
else()
    # 纯代码模式：使用纯代码版本的头文件
    file(GLOB HEADERS
        "include/core/loginwindow.h"
        "include/core/mainwindow.h"
        "include/core/capture.h"
        "include/utils/*.h"
        "include/api/*.h"
    )
endif()

# 添加资源文件
set(RESOURCE_FILES
    resources/resources.qrc
)

# 创建可执行文件
add_executable(${PROJECT_NAME} ${SOURCES} ${HEADERS} ${UI_FILES} ${RESOURCE_FILES})

# 设置包含目录
target_include_directories(${PROJECT_NAME} PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}/include
    ${CMAKE_CURRENT_BINARY_DIR}  # 用于生成的ui_*.h文件
)

# 设置UI文件搜索路径
if(USE_UI_FILES)
    set(CMAKE_AUTOUIC_SEARCH_PATHS ${CMAKE_CURRENT_SOURCE_DIR}/ui)
endif()

# 链接 Qt 库
target_link_libraries(${PROJECT_NAME} PRIVATE
    Qt5::Core
    Qt5::Gui
    Qt5::Widgets
    Qt5::Network
)

# 添加fxmy库路径
link_directories(${CMAKE_CURRENT_SOURCE_DIR}/lib)

# 链接 fxmy 库 (仅Windows平台支持)
if(WIN32)
    target_link_libraries(${PROJECT_NAME} PRIVATE
        fxmy
    )
    message(STATUS "Linking with fxmy library (Windows only)")
else()
    message(WARNING "fxmy library is only available on Windows platform. Building without fxmy support for development purposes.")
endif()

# 设置输出目录
set_target_properties(${PROJECT_NAME} PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}"
)

# 设置autogen目录位置（隐藏在build目录中）
# 注意：autogen目录是Qt构建过程的正常部分，包含重要的生成文件
# 在Qt Creator中可以通过项目设置隐藏此目录的显示

# 平台特定配置
if(WIN32)
    # Windows 特定配置
    set_target_properties(${PROJECT_NAME} PROPERTIES
        WIN32_EXECUTABLE TRUE
        OUTPUT_NAME "aiwuu-classroom"
    )
    # 添加图标
    set(APP_ICON_RESOURCE_WINDOWS "${CMAKE_SOURCE_DIR}/resources/icon.ico")
    add_custom_command(TARGET ${PROJECT_NAME} POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E copy_if_different
        "${APP_ICON_RESOURCE_WINDOWS}"
        $<TARGET_FILE_DIR:${PROJECT_NAME}>
    )
    
    # 复制 Qt DLL 到输出目录
    add_custom_command(TARGET ${PROJECT_NAME} POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E copy_if_different
        $<TARGET_FILE:Qt5::Core>
        $<TARGET_FILE:Qt5::Gui>
        $<TARGET_FILE:Qt5::Widgets>
        $<TARGET_FILE:Qt5::Network>
        $<TARGET_FILE_DIR:${PROJECT_NAME}>
    )
    
    # 复制 fxmy bin 目录到输出目录（按照文档要求）
    add_custom_command(TARGET ${PROJECT_NAME} POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E copy_directory
        "${CMAKE_CURRENT_SOURCE_DIR}/bin"
        $<TARGET_FILE_DIR:${PROJECT_NAME}>
    )

    # 设置 Windows 特定的编译选项
    target_compile_definitions(${PROJECT_NAME} PRIVATE
        WIN32_LEAN_AND_MEAN
        NOMINMAX
    )

    # 设置 Windows 特定的链接选项
    set_target_properties(${PROJECT_NAME} PROPERTIES
        LINK_FLAGS "/SUBSYSTEM:WINDOWS /ENTRY:mainCRTStartup"
    )
elseif(APPLE)
    # macOS 特定配置
    set_target_properties(${PROJECT_NAME} PROPERTIES
        MACOSX_BUNDLE TRUE
        MACOSX_BUNDLE_GUI_IDENTIFIER "com.aiwuu.classroom"
        MACOSX_BUNDLE_BUNDLE_NAME "AIWUU Classroom"
        MACOSX_BUNDLE_BUNDLE_VERSION "${PROJECT_VERSION}"
        MACOSX_BUNDLE_SHORT_VERSION_STRING "${PROJECT_VERSION}"
    )
    
    # 复制 fxmy bin 目录到输出目录（按照文档要求）
    add_custom_command(TARGET ${PROJECT_NAME} POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E copy_directory
        "${CMAKE_CURRENT_SOURCE_DIR}/bin"
        $<TARGET_FILE_DIR:${PROJECT_NAME}>
    )

    # 添加图标（如果存在）
    set(APP_ICON_RESOURCE_MACOSX "${CMAKE_SOURCE_DIR}/resources/icon.icns")
    if(EXISTS "${APP_ICON_RESOURCE_MACOSX}")
        set_source_files_properties(${APP_ICON_RESOURCE_MACOSX} PROPERTIES
            MACOSX_PACKAGE_LOCATION "Resources"
        )
        target_sources(${PROJECT_NAME} PRIVATE ${APP_ICON_RESOURCE_MACOSX})
    endif()
else()
    # Linux 特定配置
    install(TARGETS ${PROJECT_NAME}
        RUNTIME DESTINATION bin
    )
    # 安装桌面文件
    install(FILES "${CMAKE_SOURCE_DIR}/resources/aiwuu-classroom.desktop"
        DESTINATION share/applications
    )
endif()

# 设置开发环境变量
if(ENV_DEV)
    target_compile_definitions(${PROJECT_NAME} PRIVATE ENV_DEV)
endif()

# 设置Qt使用标志
if(USE_QT)
    target_compile_definitions(${PROJECT_NAME} PRIVATE USE_QT=1)
    message(STATUS "Building with Qt framework enabled (USE_QT=1)")
else()
    target_compile_definitions(${PROJECT_NAME} PRIVATE USE_QT=0)
    message(STATUS "Building with Qt framework disabled (USE_QT=0)")
endif()

# 设置安装规则
if(APPLE)
    install(TARGETS ${PROJECT_NAME}
        BUNDLE DESTINATION .
        RUNTIME DESTINATION bin
    )
else()
    install(TARGETS ${PROJECT_NAME}
        RUNTIME DESTINATION bin
    )
endif()

# 环境配置
option(ENV_DEV "Build for development environment" ON)
if(ENV_DEV)
    target_compile_definitions(${PROJECT_NAME} PRIVATE ENV_DEV)
    target_sources(${PROJECT_NAME} PRIVATE include/core/config.dev.h)
    message(STATUS "Building for development environment")
else()
    target_compile_definitions(${PROJECT_NAME} PRIVATE ENV_PROD)
    target_sources(${PROJECT_NAME} PRIVATE include/core/config.prod.h)
    message(STATUS "Building for production environment")
endif()
