#!/bin/bash

# 设置颜色输出
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

# 打印带颜色的消息
print_message() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# 检查命令是否存在
check_command() {
    if ! command -v "$1" &> /dev/null; then
        print_error "$1 未安装"
        exit 1
    fi
}

# 显示帮助信息
show_help() {
    echo "用法: ./build.sh [选项]"
    echo "选项:"
    echo "  -p, --platform <平台>    指定目标平台 (windows/linux/macos)"
    echo "  -e, --env <环境>        指定构建环境 (dev/prod)"
    echo "  -h, --help             显示帮助信息"
    echo ""
    echo "示例:"
    echo "  ./build.sh -p windows -e prod    # 构建 Windows 生产版本"
    echo "  ./build.sh -p linux -e prod     # 构建 Linux 生产版本"
    echo "  ./build.sh -p macos -e prod     # 构建 macOS 生产版本"
}

# 主函数
main() {
    local platform=""
    local env="prod"
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case "$1" in
            -p|--platform)
                platform="$2"
                shift 2
                ;;
            -e|--env)
                env="$2"
                shift 2
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                echo "未知选项: $1"
                exit 1
                ;;
        esac
    done
    
    # 检查必要参数
    if [ -z "$platform" ]; then
        echo "错误: 必须指定平台"
        exit 1
    fi
    
    # 检查环境
    if [[ "$env" != "dev" && "$env" != "prod" ]]; then
        print_error "不支持的环境 '$env'"
        exit 1
    fi
    
    # 设置Qt5路径
    export PATH="/opt/homebrew/opt/qt@5/bin:$PATH"
    
    # 设置构建目录
    BUILD_DIR="build-$platform-$env"
    
    # 清理旧的构建文件
    print_message "清理旧的构建文件..."
    rm -rf "$BUILD_DIR"
    mkdir -p "$BUILD_DIR"
    cd "$BUILD_DIR" || exit 1
    
    # 配置构建参数
    local CMAKE_ARGS=()
    
    if [[ "$env" == "dev" ]]; then
        CMAKE_ARGS+=("-DBUILD_FOR_DEV=ON")
        CMAKE_ARGS+=("-DCMAKE_BUILD_TYPE=Debug")
    else
        CMAKE_ARGS+=("-DBUILD_FOR_DEV=OFF")
        CMAKE_ARGS+=("-DCMAKE_BUILD_TYPE=Release")
    fi
    
    # 平台特定配置
    case "$platform" in
        windows)
            print_message "配置 Windows 构建..."
            ;;
        linux)
            print_message "配置 Linux 构建..."
            ;;
        macos)
            print_message "配置 macOS 构建..."
            ;;
        *)
            print_error "不支持的平台: $platform"
            exit 1
            ;;
    esac
    
    # 运行 cmake
    print_message "配置构建系统..."
    cmake "${CMAKE_ARGS[@]}" ..
    
    if [ $? -ne 0 ]; then
        print_error "cmake 配置失败"
        exit 1
    fi
    
    # 编译项目
    print_message "编译项目..."
    cmake --build . --parallel $(sysctl -n hw.ncpu)
    
    if [ $? -ne 0 ]; then
        print_error "编译失败"
        exit 1
    fi
    
    print_message "构建成功完成!"
    print_message "构建输出位于: $(pwd)"
}

# 执行主函数
main "$@"
