# UI模式切换指南

## 概述

本项目支持两种UI开发模式，可以通过简单的命令在两种模式之间切换：

1. **纯代码模式** (默认) - 使用纯C++代码创建界面
2. **UI文件模式** - 使用Qt Designer的.ui文件，支持可视化编辑

## 快速切换

### 使用切换脚本 (推荐)

```bash
# 查看当前模式
./switch_ui_mode.sh status

# 切换到UI文件模式
./switch_ui_mode.sh ui

# 切换到纯代码模式
./switch_ui_mode.sh code

# 查看帮助
./switch_ui_mode.sh help
```

### 手动切换

#### 切换到UI文件模式
```bash
# 清理并重新构建
rm -rf build
mkdir build && cd build

# 配置为UI模式
cmake -DUSE_UI_FILES=ON ..

# 构建
make -j4
```

#### 切换到纯代码模式
```bash
# 清理并重新构建
rm -rf build
mkdir build && cd build

# 配置为纯代码模式
cmake -DUSE_UI_FILES=OFF ..

# 构建
make -j4
```

## 两种模式对比

| 特性 | 纯代码模式 | UI文件模式 |
|------|------------|------------|
| **开发方式** | 手写C++代码 | Qt Designer可视化编辑 |
| **灵活性** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ |
| **易用性** | ⭐⭐ | ⭐⭐⭐⭐⭐ |
| **学习曲线** | 陡峭 | 平缓 |
| **团队协作** | 需要Qt知识 | 设计师友好 |
| **版本控制** | 代码diff清晰 | XML格式 |
| **性能** | 略好 | 相当 |
| **调试** | 容易 | 中等 |

## 文件结构对比

### 纯代码模式使用的文件
```
src/core/
├── main.cc                     # 程序入口
├── loginwindow.cc              # 登录窗口实现
└── mainwindow.cc               # 主窗口实现

include/core/
├── loginwindow.h               # 登录窗口头文件
└── mainwindow.h                # 主窗口头文件
```

### UI文件模式使用的文件
```
ui/
├── loginwindow.ui              # 登录窗口UI文件
└── mainwindow.ui               # 主窗口UI文件

src/core/
├── main_ui.cc                  # 程序入口
├── loginwindow_ui.cc           # 登录窗口实现
└── mainwindow_ui.cc            # 主窗口实现

include/core/
├── loginwindow_ui.h            # 登录窗口头文件
└── mainwindow_ui.h             # 主窗口头文件
```

## 开发工作流

### 纯代码模式工作流
1. 在代码编辑器中编写UI代码
2. 编译运行查看效果
3. 修改代码调整界面
4. 重复步骤2-3

### UI文件模式工作流
1. 在Qt Creator中打开.ui文件
2. 使用Qt Designer可视化编辑界面
3. 保存.ui文件
4. 编译运行查看效果
5. 如需要，在代码中添加业务逻辑

## 切换注意事项

### 1. 数据备份
切换模式前建议备份重要修改，虽然两种模式的源文件是分离的，但构建配置会改变。

### 2. 构建清理
切换模式时会自动清理构建目录，确保没有缓存的构建文件影响新模式。

### 3. IDE配置
- **Qt Creator**: 两种模式都完全支持
- **其他IDE**: 纯代码模式支持更好，UI文件模式可能需要额外配置

### 4. 版本控制
两种模式的源文件都会被版本控制跟踪，可以安全地在团队中切换。

## 实际使用建议

### 适合纯代码模式的场景
- 需要复杂的动态界面
- 大量程序化生成的控件
- 对性能有极高要求
- 团队都有Qt开发经验

### 适合UI文件模式的场景
- 快速原型开发
- 静态界面布局
- 团队有UI/UX设计师
- 需要频繁调整界面样式

### 混合开发策略
可以在项目的不同阶段使用不同模式：
1. **原型阶段**: 使用UI文件模式快速搭建界面
2. **开发阶段**: 根据需要选择合适的模式
3. **优化阶段**: 如需要，可以将UI文件转换为纯代码

## 故障排除

### 切换失败
如果切换失败，尝试：
```bash
# 完全清理
rm -rf build
rm -rf CMakeCache.txt

# 重新切换
./switch_ui_mode.sh [ui|code]
```

### 编译错误
- **UI模式编译错误**: 检查.ui文件是否正确，对象名称是否匹配
- **纯代码模式编译错误**: 检查C++代码语法，头文件包含是否正确

### Qt Creator识别问题
1. 重新打开项目
2. 清理CMake配置: `Tools -> CMake -> Clear CMake Configuration`
3. 重新配置: `Tools -> CMake -> Configure Project`

## 示例：添加新控件

### 纯代码模式
```cpp
// 在loginwindow.cc中添加
QPushButton *newButton = new QPushButton("新按钮", this);
newButton->setObjectName("newButton");
layout->addWidget(newButton);
connect(newButton, &QPushButton::clicked, this, &LoginWindow::onNewButtonClicked);
```

### UI文件模式
1. 在Qt Designer中拖拽QPushButton到界面
2. 设置objectName为"newButton"
3. 设置text为"新按钮"
4. 在代码中添加信号槽连接：
```cpp
// 在loginwindow_ui.cc的connectSignals()中添加
connect(ui->newButton, &QPushButton::clicked, this, &LoginWindowUI::onNewButtonClicked);
```

## 总结

两种模式各有优势，选择哪种模式取决于：
- 团队技能水平
- 项目需求复杂度
- 开发时间要求
- 维护便利性需求

建议新手从UI文件模式开始，熟悉Qt开发后再考虑纯代码模式。 