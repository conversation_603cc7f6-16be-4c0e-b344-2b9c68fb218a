#!/bin/bash

# UI模式切换脚本
# 用法: ./switch_ui_mode.sh [ui|code]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 显示帮助信息
show_help() {
    echo -e "${BLUE}UI模式切换脚本${NC}"
    echo ""
    echo "用法:"
    echo "  $0 ui      - 切换到UI文件模式"
    echo "  $0 code    - 切换到纯代码模式"
    echo "  $0 status  - 显示当前模式"
    echo "  $0 help    - 显示此帮助信息"
    echo ""
    echo "模式说明:"
    echo "  UI文件模式   - 使用Qt Designer的.ui文件，支持可视化编辑"
    echo "  纯代码模式   - 使用纯C++代码创建界面，更灵活但需要手写"
}

# 获取当前模式
get_current_mode() {
    if [ -f "build/CMakeCache.txt" ]; then
        if grep -q "USE_UI_FILES:BOOL=ON" build/CMakeCache.txt; then
            echo "ui"
        else
            echo "code"
        fi
    else
        echo "unknown"
    fi
}

# 显示当前状态
show_status() {
    current_mode=$(get_current_mode)
    case $current_mode in
        "ui")
            echo -e "${GREEN}当前模式: UI文件模式${NC}"
            echo "  - 使用 Qt Designer 的 .ui 文件"
            echo "  - 支持可视化编辑界面"
            echo "  - 入口文件: src/core/main_ui.cc"
            echo "  - 登录窗口: src/core/loginwindow_ui.cc"
            ;;
        "code")
            echo -e "${YELLOW}当前模式: 纯代码模式${NC}"
            echo "  - 使用纯 C++ 代码创建界面"
            echo "  - 更灵活的界面控制"
            echo "  - 入口文件: src/core/main.cc"
            echo "  - 登录窗口: src/core/loginwindow.cc"
            ;;
        "unknown")
            echo -e "${RED}未知模式 (项目尚未构建)${NC}"
            echo "请先运行构建命令"
            ;;
    esac
}

# 切换到UI模式
switch_to_ui() {
    echo -e "${BLUE}切换到UI文件模式...${NC}"
    
    # 清理构建目录
    if [ -d "build" ]; then
        echo "清理构建目录..."
        rm -rf build
    fi
    
    # 创建构建目录
    mkdir -p build
    cd build
    
    # 配置CMake
    echo "配置CMake (UI模式)..."
    cmake -DUSE_UI_FILES=ON ..
    
    # 构建项目
    echo "构建项目..."
    make -j$(nproc 2>/dev/null || sysctl -n hw.ncpu 2>/dev/null || echo 4)
    
    cd ..
    echo -e "${GREEN}✓ 已切换到UI文件模式${NC}"
    echo "现在可以在Qt Creator中编辑 ui/loginwindow.ui 文件"
}

# 切换到纯代码模式
switch_to_code() {
    echo -e "${BLUE}切换到纯代码模式...${NC}"
    
    # 清理构建目录
    if [ -d "build" ]; then
        echo "清理构建目录..."
        rm -rf build
    fi
    
    # 创建构建目录
    mkdir -p build
    cd build
    
    # 配置CMake
    echo "配置CMake (纯代码模式)..."
    cmake -DUSE_UI_FILES=OFF ..
    
    # 构建项目
    echo "构建项目..."
    make -j$(nproc 2>/dev/null || sysctl -n hw.ncpu 2>/dev/null || echo 4)
    
    cd ..
    echo -e "${GREEN}✓ 已切换到纯代码模式${NC}"
    echo "现在使用纯C++代码创建界面"
}

# 主逻辑
case "${1:-help}" in
    "ui")
        switch_to_ui
        ;;
    "code")
        switch_to_code
        ;;
    "status")
        show_status
        ;;
    "help"|"--help"|"-h")
        show_help
        ;;
    *)
        echo -e "${RED}错误: 未知参数 '$1'${NC}"
        echo ""
        show_help
        exit 1
        ;;
esac 