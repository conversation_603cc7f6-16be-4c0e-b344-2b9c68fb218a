@echo off
REM Windows运行脚本 - 用于虚拟机环境
REM 用法: run_windows.bat

echo 🚀 启动 Aiwuu Classroom (Windows)
echo ================================

REM 设置编码为UTF-8
chcp 65001 > nul

REM 检查可执行文件是否存在
if not exist "build\aiwuu-classroom.exe" (
    echo ❌ 可执行文件不存在，正在构建...
    call build_windows.bat
    if %ERRORLEVEL% neq 0 (
        echo ❌ 构建失败，无法运行
        pause
        exit /b 1
    )
)

REM 运行应用程序
echo ▶️ 启动应用程序...
cd build
aiwuu-classroom.exe
cd ..

echo ✅ 应用程序已退出
pause
