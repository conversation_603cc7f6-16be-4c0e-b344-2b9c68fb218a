@echo off
REM Windows构建脚本 - 用于虚拟机环境
REM 用法: build_windows.bat

echo 🔧 Windows环境构建脚本
echo ========================

REM 设置编码为UTF-8
chcp 65001 > nul

REM 检查是否存在构建目录
if exist "build" (
    echo 📁 清理旧的构建目录...
    rmdir /s /q build
)

REM 创建构建目录
echo 📁 创建构建目录...
mkdir build
cd build

REM 配置CMake (Windows环境)
echo ⚙️ 配置CMake...
cmake -G "MinGW Makefiles" -DUSE_UI_FILES=ON -DCMAKE_BUILD_TYPE=Debug ..

if %ERRORLEVEL% neq 0 (
    echo ❌ CMake配置失败
    pause
    exit /b 1
)

REM 构建项目
echo 🔨 构建项目...
cmake --build . --parallel 4

if %ERRORLEVEL% neq 0 (
    echo ❌ 构建失败
    pause
    exit /b 1
)

echo ✅ 构建成功！
echo 📍 可执行文件位置: build\aiwuu-classroom.exe

cd ..
pause
