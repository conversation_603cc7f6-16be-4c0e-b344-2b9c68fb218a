/* 登录窗口容器 */
#loginContainer {
    background-color: white;
    border-radius: 10px;
}

/* 标题栏 */
#titleBar {
    background-color: transparent;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
}

/* 窗口控制按钮容器 */
#controlButtons {
    background-color: transparent;
}

/* 关闭按钮 */
#closeButton {
    background-color: #ff5f57;
    border: none;
    border-radius: 6px;
    color: #4d0000;
    font-size: 12px;
    font-weight: bold;
}

#closeButton:hover {
    background-color: #ff3b30;
}

/* 最小化按钮 */
#minButton {
    background-color: #febc2e;
    border: none;
    border-radius: 6px;
    color: #995700;
    font-size: 12px;
    font-weight: bold;
}

#minButton:hover {
    background-color: #ffcc00;
}

/* 最大化按钮 */
#maxButton {
    background-color: #28c840;
    border: none;
    border-radius: 6px;
    color: #006500;
    font-size: 12px;
    font-weight: bold;
}

#maxButton:hover {
    background-color: #34c759;
}

/* 左侧面板 */
#leftPanel {
    background-color: transparent;
}

/* 右侧面板 */
#rightPanel {
    background-color: white;
    border-top-right-radius: 8px;
    border-bottom-right-radius: 8px;
}

/* 标题容器 */
#titleContainer {
    margin-bottom: 20px;
}

/* 用户名容器 */
#usernameContainer {
    margin-bottom: 6px;
}

/* 密码容器 */
#passwordContainer {
    margin-bottom: 40px;
}

/* 标题样式 */
#titleLabel {
    font-size: 32px;
    font-weight: bold;
    color: #1f2937;
}

/* 副标题样式 */
#subtitleLabel {
    font-size: 16px;
    color: #6b7280;
    margin-top: 4px;
}

/* 输入框标签 */
#usernameLabel, #passwordLabel {
    font-size: 14px;
    color: #333;
    margin-bottom: 4px;
}

/* 输入框样式 */
QLineEdit {
    padding: 0 12px;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 14px;
    color: #333;
    height: 50px;
    background-color: white;
}

QLineEdit:focus {
    border-color: #722ed1;
    background-color: white;
}

QLineEdit::placeholder {
    color: #999;
}

/* 登录按钮 */
#loginButton {
    background-color: #722ed1;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 500;
    height: 45px;
    margin-bottom: 6px;
}

#loginButton:hover {
    background-color: #9254de;
}

#loginButton:pressed {
    background-color: #531dab;
}

/* 忘记密码按钮 */
#forgotButton {
    background-color: transparent;
    color: #722ed1;
    border: none;
    font-size: 14px;
    margin-top: 6px;
}

#forgotButton:hover {
    color: #9254de;
} 