**obj
**x64
**Debug
**Release
.vs
.idea
.DS_Store

# CMake build directory
build/
cmake-build-*/

# Qt Creator user files
CMakeLists.txt.user
CMakeLists.txt.user.*

# Qt cache and temporary files
.cache/
*.autosave
*.pro.user
*.pro.user.*

# macOS
Thumbs.db
.DS_Store

# Logs
logs/
*.log

# Icon creation temporary files
app.iconset/

# Compiled Object files
*.o
*.obj

# Compiled Dynamic libraries
*.so
*.dylib
*.dll

# Compiled Static libraries
*.a
*.lib

# Executables
*.exe
*.out
*.app