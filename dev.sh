#!/bin/bash

# 设置错误时退出
set -e

# 自动检测平台
if [[ "$OSTYPE" == "darwin"* ]]; then
    PLATFORM="macos"
elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
    PLATFORM="linux"
elif [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" ]]; then
    PLATFORM="windows"
else
    echo "不支持的操作系统: $OSTYPE"
    exit 1
fi

# 自动检测 Qt 路径
if [[ "$PLATFORM" == "macos" ]]; then
    # 检查是否安装了 Homebrew
    if ! command -v brew &> /dev/null; then
        echo "未找到 Homebrew，请先安装 Homebrew"
        exit 1
    fi

    # 检查是否安装了 Qt5
    if ! brew list qt@5 &> /dev/null; then
        echo "未找到 Qt5，正在安装..."
        brew install qt@5
    fi

    # 获取 Qt5 的安装路径
    QT_PATH=$(brew --prefix qt@5)
    if [ -z "$QT_PATH" ]; then
        echo "无法获取 Qt5 的安装路径"
        exit 1
    fi

    # 设置 Qt 环境变量
    export CMAKE_PREFIX_PATH="$QT_PATH/lib/cmake"
    export LDFLAGS="-L$QT_PATH/lib"
    export CPPFLAGS="-I$QT_PATH/include"
    export PKG_CONFIG_PATH="$QT_PATH/lib/pkgconfig"
elif [[ "$PLATFORM" == "linux" ]]; then
    # Linux 下的 Qt 路径检测
    if [ -d "/usr/lib/qt5" ]; then
        export CMAKE_PREFIX_PATH="/usr/lib/qt5"
    elif [ -d "/usr/lib/x86_64-linux-gnu/qt5" ]; then
        export CMAKE_PREFIX_PATH="/usr/lib/x86_64-linux-gnu/qt5"
    else
        echo "未找到 Qt5 安装路径，请确保已安装 Qt5"
        exit 1
    fi
elif [[ "$PLATFORM" == "windows" ]]; then
    # Windows 下的 Qt 路径检测
    if [ -d "C:/Qt/5.15.2/msvc2019_64" ]; then
        export CMAKE_PREFIX_PATH="C:/Qt/5.15.2/msvc2019_64"
    elif [ -d "C:/Qt/5.15.2/mingw81_64" ]; then
        export CMAKE_PREFIX_PATH="C:/Qt/5.15.2/mingw81_64"
    else
        echo "未找到 Qt5 安装路径，请确保已安装 Qt5"
        exit 1
    fi
fi

# 解析命令行参数
CLEAN_BUILD=false
while [[ $# -gt 0 ]]; do
    case $1 in
        -c|--clean)
            CLEAN_BUILD=true
            shift
            ;;
        -p|--platform)
            PLATFORM="$2"
            shift 2
            ;;
        *)
            echo "未知参数: $1"
            exit 1
            ;;
    esac
done

# 创建构建目录
if [ ! -d "build" ]; then
    mkdir build
fi

# 清理构建目录
if [ "$CLEAN_BUILD" = true ]; then
    echo "清理构建目录..."
    rm -rf build/*
fi

# 进入构建目录
cd build

# 配置项目
echo "配置项目..."
cmake ..

# 构建项目
echo "构建项目..."
make -j$(nproc)

# 运行程序
echo "运行程序..."
if [[ "$PLATFORM" == "macos" ]]; then
    ./aiwuu-classroom.app/Contents/MacOS/aiwuu-classroom
elif [[ "$PLATFORM" == "linux" ]]; then
    ./aiwuu-classroom
elif [[ "$PLATFORM" == "windows" ]]; then
    ./aiwuu-classroom.exe
fi
